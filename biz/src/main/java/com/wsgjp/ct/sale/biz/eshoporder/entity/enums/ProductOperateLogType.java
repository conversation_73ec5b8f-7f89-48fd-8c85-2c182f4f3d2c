package com.wsgjp.ct.sale.biz.eshoporder.entity.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020-03-11 15:16
 */
public enum ProductOperateLogType  implements CodeEnum {

    Refresh(1,"刷新网店商品", true),
    DownloadProduct(2,"下载网店商品",false),
    AutoMatchXCode(3,"按照商家编码自动对应", false),
    ChangeXCode(6,"修改商家编码", true),
    BindMapping(7,"手工绑定",true),
    AddMapping(8,"增", false),
    LocalPtypeUnable(9,"启用删除或停用本地商品或套餐", true),
    ClearMapping(10,"手工解绑", true),
    TempMapping(11,"临时对应", true),
    ChangeLocalXcode(12,"新增/修改商家编码或套餐编码", true),
    ChangeRule(13,"切换对应规则为:按商家编码自动对应", false),
    ChangeRuleManual(14,"切换对应规则为:手工对应", false),
    MergePtype(16,"合并商品", true),
    ModifyPtypeSkuInfo(28,"修改商品基础信息", true),
    BatchChangeOnlineXCode(18,"批量生成商家编码", true),
    ClearMappingBatch(20,"批量解绑", false),
    BindMappingBatch(21,"批量绑定", false),
    BatchRefresh(22,"批量刷新网店商品", false),
    ExportBindMapping(23,"导入绑定", false),
    AutoBatchRefresh(24,"自动刷新网店商品", true),
    YuanQiPushBind(25,"元气推送解/绑", true),
    AutoMatchByBarcode(26,"按照条码自动对应", false),
    ModifyPtypeInfo(27,"修改商品信息", true),
    DeletePtypeSkuInfo(29,"删除SKU属性值", false),
    ModifyMappingTypeByHands(30,"按手工对应", false),
    ModifyMappingTypeByXcode(31,"按商家编码对应", false),
    DELETEPLATFORMPRODUCT(32,"删除网店商品sku", false),
    BATCHADDLOCALPTYPE(33,"批量增加本地属性商品", true),
    ADDLOCALNORMALPTYPE(34,"增加本地普通商品", true),
    ADDONELOCALPTYPE(35,"增加一个本地属性商品", false),
    GOOD_ANALYSIS(36,"商品解析", true),
    DELIVER_UPDATE(37,"订单处理更新商品对应关系", true),
    DELETEPRODUCT_BY_ORDER(38,"下载订单删除商品对应关系", true),
    SYNC_RELATION_TO_BRANCH(39,"同步对应关系到子店", true),
    DOWNLOAD_MAIN_IMAGE(40,"下载网店商品主图做商品主图", true),
    MANUAL_MAPPING_UNBIND(41,"解除手工绑定", true),
    DELETE_SKU(42,"删除SKU", true),
    MODIFY_MARK(43,"编辑标记", true),
    CLEAN_MARK(44, "清除标记", true),
    ADD_MARK(45, "添加标记", true),
    OPEN_SYNC_STOCK(46,"开启自动同步", true),
    CLOSE_SYNC_STOCK(47,"关闭自动同步", true),
    BIND_STOCK_RULE(48,"绑定库存规则", true),
    ;


    private int index;
    private String name;

    private Boolean newVersionShowEnabled;

    ProductOperateLogType(int index,String name, Boolean newVersionShowEnabled){
        this.index=index;
        this.name=name;
        this.newVersionShowEnabled = newVersionShowEnabled;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public Boolean getNewVersionShowEnabled(){
        return this.newVersionShowEnabled;
    }

    @Override
    public String getName() {
        return name;
    }


}
