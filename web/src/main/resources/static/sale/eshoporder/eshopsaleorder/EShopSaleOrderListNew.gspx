<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="${title}"
      ActionType="sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction,sale/eshoporder/eshopsaleorder/EShopSaleOrderListNew.js"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="Craba.UI"
      CssClass='pd0 jarvis shglPage'>
    <HiddenField ID="hideBuyerInfo"/>
    <HiddenField ID="hideInvoiceInfo"/>
    <HiddenField ID="nowEventArgs"/>
    <Style Url="sale/jarvis/skins/common.css"/>
    <FlexBlock >
        <FlexColumn Width='320'  CssClass='LeftBlock' ID="customerQueryFlexColumn">
            <FlowPanel  AllowConfig="true"  ConfigMode="DragLabel" BusinessEx='{"Name":"erp.dynamicNavMenu","ids":"leftPanel"}'
                        LayoutDirection="Vert" ItemCssClass="FlexAuto" ID="leftPanel"  _hideModifyIcon="true"
                       PopupIds="createType,businessType,orderState,areas,orderStateNav,ptypeNav,freightNav,memoNav,qualityNav,gatherStatus"
                       CssClass='HideMoreButton pd10 FlexAuto MB0 myCssss' CustomScrollBar='true' StaticIds="moreid,btmBlock"
                        NotConfigIds="timeType,quickDate,timeRange,quickQueryList,block4,timeNavMenu,btmBlock">
                <Block CssClass="TableEdit" ID="timeNavMenu" Tag="时间条件" _quickIcon="bicon-rili">
                    <HBlock CssClass='dflex' ID="'timeBlock">
                        <DropDownEdit Text="时间类型" ReportField="时间类型" SelectedIndex="0" DropDownStyle="DropDownList"
                                      ID="timeType" ListItems="0=拍下时间,1=付款时间,2=订单创建时间"
                                       DataField="advanceTimeType" />
                        <Buttons CssStyle="margin-left: -1px;margin-top: 0px;" ID="quickDate" MustOne="true" CssClass="FlexAuto" ListItems='0=今天,2=近3天,99=更多' OnClick="doQuickDateQuery" Value='2' />
                    </HBlock>
                    <DateRangeEdit MaxValue="0" ReportVisible="true" ReportField="时间范围" ID="dateRange"
                                   DataEndField="endTime" DataStartField="beginTime"
                                   DataField="timeRange" CssClass='FlexAuto'
                                   OnChange="" LayoutDirection="Vert"
                                   NullDisplayText='选择开始时间和结束时间'/>
                </Block>

                <Block CssClass="TableEdit TableEditPlus" ID="block4">
                    <DropDownEdit CssClass='FlexAuto' ID="filterKeyTypeNew" OnChange="filterKeyChange"
                                  SelectedIndex="0" DataField="filterKeyTypeNew" OuterCssClass='border-bottom0'
                                  ItemCssClass='mbf0' ReportVisible="false"
                                  DataSource="${filterKeyTypeNew}" DropDownStyle="DropDownList"
                                  DataTextField="name" DataValueField="code">
                    </DropDownEdit>
                    <MemoEdit ReportField="订单编号" ID="keyWord" CssClass='FlexAuto bgc border-top0 NoBorder'
                              Height="60"
                              DataField="keyWord" ReportVisible="true"
                              NullDisplayText="支持复制多个订单编号，分隔符使用逗号','，空格或回车"/>
                    <Block CssClass="check-bangzhu" ID="contanisJdQueryBlock" Visible="false">
                        <CheckBox Text="查询京东系订单" ID="contanisJdQuery" OnChange="JdQueryChange" Value="true"/>
                        <SpeedButton Width="30" Icon="bicon-bangzhu" Hint="${jDHtml}"  CssClass="jdHelpButton"/>
                    </Block>
                </Block>
                <ListView ID="quickQueryList" Tag="快速查询" DataKeyField="id" SelectField='quickQueryList' ItemCssField="itemClass" OnItemClick="doQuickQueryAllClick"
                          CssClass="List ButtonView mb10 Radio" UseSelect="true" OnItemRendering="doQuickQueryItemRenderx"  _quickIcon="bicon-xiaoshouchaxun">
                    <ListTemplate>
                        <DataText DataField="text" />
                        <DataText DataField="value" CssClass="value" DataCssField='valueClass' />
                        <DataText CssClass="aicon-moreunfold " ID="expand"  Visible="false" />
                    </ListTemplate>
                </ListView>

                <ListView Visible="true" ID="customQueryList" Tag="自定义查询" DataKeyField="id" SelectField='quickQueryList' ItemCssField="itemClass"
                          CssClass="List ButtonView mb10 fullWidth" UseSelect="true" OnItemRendering="doCustomQueryItemRenderx" OnItemClick="doCustomQueryClick" _quickIcon="bicon-xiaoshouchaxun">
                    <ListTemplate>
                        <DataText DataField="customerQuery.title" />
                        <DataText DataField="num" CssClass="value" DataCssField='valueClass' />
                        <DataText CssClass="aicon-moreunfold" ID="expand" Visible="false"/>
                    </ListTemplate>
                </ListView>

                <DropDownCheckBoxList Label="网店：" ID="eshop" DropDownStyle="DropDownSearch" OnChange="filterKeyChange"
                                      MaxWidth="0" ItemCssClass="ItemEdit"  ReportVisible="true"
                                      DataSource="${eshops}" DataField="otypeId"
                                      DataTextField="fullname" DataValueField="otypeId"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"/>

                <DropDownCheckBoxList Label="标记：" ID="mark" DropDownStyle="DropDownSearch"
                                      MaxWidth="0" ItemCssClass="ItemEdit"  ReportVisible="true"
                                      DataSource="${markTypeList}" DataField="mentionValues"
                                      DataTextField="name" DataValueField="code"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"/>

                <DropDownCheckBoxList Label="旗帜：" ID="flag" DropDownStyle="DropDownSearch"
                                      MaxWidth="0" ItemCssClass="ItemEdit"  ReportVisible="true" DropType='Image'
                                      DataSource="${flagsSource}" DataField="sellerFlag"
                                      DataTextField="name" DataValueField="code"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"/>

                <DropDownCheckBoxList Label="创建方式：" ID="createType" DropDownStyle="DropDownSearch"
                                      ItemCssClass="ItemEdit"  ReportVisible="true"
                                      DataSource="${createType}" DataField="createType"
                                      DataTextField="name" DataValueField="code"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"/>
                <DropDownCheckBoxList Label="业务类型：" ID="businessType" DropDownStyle="DropDownSearch"
                                      ItemCssClass="ItemEdit"  ReportVisible="true"
                                      DataSource="${businessTypes}" DataField="createType"
                                      DataTextField="name" DataValueField="code"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"/>
                <SelectorEdit Label="收货地址："
                              ReportField="收货地址" ReportVisible="false"
                              SelectorHeight="600" SelectorWidth="340" OnButtonClick="areasSelectorInit"
                              OnSelectorSelected="selectArea" OnChange="clearArea"   _quickIcon="bicon-shouhuodizhi"
                              DisplayField="areasInfo" DataField="areas" ID="areas" Visible="false"/>
                <NavBar ID="orderStateNav" CssClass="FlexAuto" ItemCssClass="mb5" Tag='订单状态：' >
                    <NavGroup Text="订单状态" Expanded="true" GroupName="GroupA"    _quickIcon="bicon-danjuzhuangtai">
                        <Block CssClass="TableEdit">
                            <Block CssClass="ItemEdit">
                                <Label Text="线上交易状态:"/>
                                <DropDownCheckBoxList Label="线上交易状态:" ReportField="线上交易状态" ID="tradeStatus" ReportVisible="true"
                                                      DropDownStyle="DropDownSearch" CssClass='FlexAuto' ItemCssClass="ItemEdit"
                                                      Width="148" DataField="tradeStatus"
                                                      DataSource="${tradeStateType}"
                                                      DataTextField="name" DataValueField="code"
                                                      LayoutDirection="Vert"/>
                            </Block>
                            <Block CssClass="ItemEdit">
                                <Label Text="退款状态:"/>
                                <DropDownCheckBoxList Label="退款状态:" ReportField="退款状态" ID="refundState" ReportVisible="true"
                                                      DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                                      Width="148" DataField="localRefundProcessState"
                                                      LayoutDirection="Vert"
                                                      DataSource="${refundStatus}" DataTextField="name"
                                                      DataValueField="code"/>
                            </Block>
                            <Block CssClass="ItemEdit">
                                <Label Text="删除状态:"/>
                                <DropDownCheckBoxList Label="删除状态:" ReportField="删除状态" ID="deleteState" ReportVisible="true"
                                                      DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                                      Width="148" DataField="deleteState"
                                                      LayoutDirection="Vert"
                                                      DataSource="${deleteState}" DataTextField="name"
                                                      DataValueField="code"/>
                            </Block>
                            <Block CssClass="ItemEdit">
                                <Label Text="导出状态:"/>
                                <DropDownCheckBoxList Label="导出状态:" ReportField="导出状态" ID="exportState" ReportVisible="true"
                                                      DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                                      Width="148" DataField="exportState"
                                                      LayoutDirection="Vert"
                                                      ListItems="0=未导出,1=已导出" DataTextField="name"
                                                      DataValueField="code"/>
                            </Block>
                            <Block CssClass="ItemEdit">
                                <Label Text="草稿状态:"/>
                                <DropDownCheckBoxList Label="草稿状态:" ReportField="草稿状态" ID="draftState" ReportVisible="true"
                                                      DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                                      Width="148" DataField="draftState"
                                                      LayoutDirection="Vert"
                                                      ListItems="0=正常,1=草稿" DataTextField="name"
                                                      DataValueField="code"/>
                            </Block>
                        </Block>
                    </NavGroup>
                </NavBar>

                <Block CssClass="TableEdit" ID="ptypeNav" Tag='商品信息'>
                    <Block CssClass="ItemEdit" CssStyle="padding-left:0px">
                        <DropDownEdit ID="ptypeType" DataField="ptypeType" Width="60"
                                      SelectedIndex="0" DropDownStyle="DropDownList" CssClass="FlexAuto"
                                      OnChange="doChangePtypeSelectType">
                            <ListItem Value="0" Text="按商品"/>
                            <ListItem Value="1" Text="按商品标签"/>
                            <ListItem Value="2" Text="按商品分类"/>
                        </DropDownEdit>
                        <SelectorEdit ID="ptypeFullnameForModify" DataField="skuIds"
                                      Business="{'Name':'jxc.ptypeColumn', 'selectType':'Sku','showOnlyBaseunit':'false'}"
                                      ShowMDI='true'
                                      DisplayField="ptypeNames" OnEnterPress="openPtypeSelect"
                                      OnButtonClick="openPtypeSelect" CssClass="FlexAuto"
                                      OnSelectorSelected="selectPtype" AutoFocusNext="false"/>
                        <DropDownCheckBoxList ReportField="商品标签" ID="ptypeLabel" DataField="ptypeLabelIds"
                                              DropDownStyle="DropDownSearch"
                                              ValueType="String" CssClass="FlexAuto"
                                              DataSource="${ptypeLabels}"
                                              DataTextField="fullname" DataValueField="id"
                                              LayoutDirection="Vert"/>
                        <SelectorEdit ReportField="商品分类" ID="ptypeClass" DataField="ptypeClassIds"
                                      SelectorPage="/jxc/baseinfo/selector/ClassSelector.gspx"
                                      ShowMDI='true'
                                      DisplayField="classNames" OnEnterPress="openPtypeClassSelect"
                                      OnButtonClick="openPtypeClassSelect" CssClass="FlexAuto"
                                      OnSelectorSelected="selectPtypeClass" AutoFocusNext="false"/>
                    </Block>
                </Block>

                <NavBar ID="freightNav" CssClass="FlexAuto" ItemCssClass="mb5" Tag='物流信息：' >
                    <NavGroup Text="物流信息" Expanded="false" GroupName="GroupB"     _quickIcon="bicon-biaojiqizhi">
                        <Block CssClass="TableEdit">
                            <Block Tag="物流公司:"  CssClass="EditBlock ItemEdit">
                                <Label Text="物流公司:"/>
                                <TextEdit ID="freightName" Width="148"
                                          Enabled="false"
                                          DataField="freightName" ReportField="物流公司" CssClass="FlexAuto"
                                          MaxLength="200"/>
                            </Block>
                            <Block Tag="物流单号:"  CssClass="EditBlock ItemEdit">
                                <Label Text="物流单号:"/>
                                <TextEdit ID="freightBillNo" Width="148"
                                          Enabled="false"
                                          DataField="freightBillNo" ReportField="物流单号" CssClass="FlexAuto"
                                          MaxLength="200"/>
                            </Block>
                        </Block>
                    </NavGroup>
                </NavBar>
                <NavBar ID="memoNav" CssClass="FlexAuto" ItemCssClass="mb5" Tag='留言/备注：' >
                    <NavGroup Text="留言/备注" Align="Left" Expanded="false" GroupName="GroupB" _quickIcon="bicon-gonghai" >
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="买家留言:" Width="60"/>
                                <DropDownEdit ID="buyerMessage"  DataField="buyerMessage" ListItems='0=不过滤,1=无留言,2=有留言,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="buyerMessageMemo" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ID="buyerMessageMemo" Width="148" NullDisplayText="留言内容..."
                                      Enabled="false" MaxLength="200"
                                      DataField="buyerMessageMemo" ReportField="买家留言" CssClass="FlexAuto"/>
                            <VSpacer Height="5"/>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="卖家备注:" Width="60"/>
                                <DropDownEdit ID="sellerMemo"  DataField="sellerMemo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="sellerMemoMessage" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ID="sellerMemoMessage" Width="148" NullDisplayText="备注内容..."
                                      Enabled="false" MaxLength="200"
                                      DataField="sellerMemoMessage" ReportField="卖家备注" CssClass="FlexAuto"/>
                            <VSpacer Height="5"/>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="系统备注:" Width="60"/>
                                <DropDownEdit ID="memo"  DataField="memo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="memoMessage" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ID="memoMessage" Width="148" NullDisplayText="备注内容..." Enabled="false"
                                      DataField="memoMessage" ReportField="系统备注" CssClass="FlexAuto" MaxLength="200"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <DropDownCheckBoxList Label="收款状态：" ID="gatherStatus" DropDownStyle="DropDownSearch"
                                      ItemCssClass="ItemEdit"  ReportVisible="true"
                                      DataSource="${gatherStatus}" DataField="gatherStatus"
                                      DataTextField="name" DataValueField="code"
                                      LayoutDirection="Vert" _quickIcon="bicon-wangdian"  />

                <Block ID="btmBlock" CssClass='BottomPanel2 pt0 FlexColumn Flex1 '>
                    <HBlock ID="moreid" CssClass="dflex Flex1">
                        <Block CssClass='Flex1'/>
                        <Label Text='更多查询条件' CssStyle="color: #2288fc;float: right;" OnClick="doShowConfig"/>
                    </HBlock>
                    <Block CssStyle="flex-direction: column;width:100%" CssClass="FlexAuto" ID="moreBlock">
                        <Block ID="bottomQueryBlock" CssClass="FlexAuto" CssStyle="justify-content: space-between;">
                            <Button Text='查询' CssClass='SpecialButton bottomQueryBlockItem' OnClick="query" ID="queryButton"  />
                            <Button Text='保存为常用查询' CssClass='SpecialButton bottomQueryBlockItem' Hint="保存自定义条件到【快速查询】" ID="customerSaveBtn" OnClick="loadCustomerQuery"/>
                        </Block>
                    </Block>

                </Block>

            </FlowPanel>
        </FlexColumn>
        <VSplitter ID="spLeftRight" ForLeft="customerQueryFlexColumn" ForRight="c_grid_Audit" CssClass='BlockGroup mr0' Mode="HideLeft"/>
        <FlexColumn ID="bRight" CssClass='pr10'>
            <FlowPanel CssClass="rightTopBtnBar" CssStyle="padding-top: 10px;" AllowConfig="true" ConfigMode='DragPanel'
                       PopupIds="btnBatchBind,btnOrderGather" StaticIds="SubmitConfig,fl1,right"
                       NotConfigIds="SubmitConfig,fl1,right">
                <Button ID="doDownload"  Text="下载订单" CssClass='SpecialButton' OnClick="doDownload" Visible="eshoporder.eshopsaleorder.downloadOrder"/>
                <Button  ID="doCreateBill" Text='新增订单'  OnClick='doCreateBill' Visible="${!isWholeSale}" Enabled="eshoporder.eshopCreateOrderPage.view"/>
                <Button  ID="doSubmit" Text="提交订单" OnClick="doSubmit" Visible="eshoporder.eshopsaleorder.submit"  PopupMenu="submitConfigPopMenu" />
                <CustomControl    Src='sale/eshoporder/common/UnRelationButton.gspx' Visible="eshoporder.eshopsaleorder.relation"  method="doRelation" CssStyle="margin-bottom: -3px"/>
                <!--                 <Button Text='修改操作' PopupMenu='menu2' Visible="false"/>-->
                <Button ID="orderOperate"  Text='订单操作' PopupMenu='menu4' Visible="${orderOperate}"/>
                <Button ID="btnBatchBind" Text="导入订单" OnClick="doShowImport" Visible="eshoporder.eshopImportOrderPage.view"/>
                <Button ID="btnOrderGather" Text="订单收款" CssClass='HasLine' Visible="${showGather}" PopupMenu='menu5' OnClick="doOrderGather"/>
                <Label CssClass='aicon-bangzhu SubmitConfigBtn SkinColor' Text='自动提交说明' ID='SubmitConfig' Visible="eshoporder.eshopsaleorder.submitConfig"/>
                <Block CssClass='Flex1' ID="fl1" NoItem='true' />
                <HBlock ID="right">
                    <Button Text="筛选" Flat="true" CssClass="aicon-filter" OnClick="doShowFilter"/>
                    <Button Flat='true' Icon='aicon-daochu' Text='导出' ID="btnExport" OnClick="doShowExportOperationPopup"
                            Visible="eshoporder.eshopsaleorder.export"/>
                </HBlock>
            </FlowPanel>
            <Grid ID="c_grid_Audit" BindPagerDataSource="bindGridData" PrimaryKey="id" OnFilter="doGetOtherFilter"
                  CssClass="maingrid margin0" PagerPageSize="200" AutoPagerPageSize="false" LazyPageSize="20"  LazyPageItem='true'
                  PagerAutoFocus="false" AllowScrollPage="false"
                  WordWrap="false" OnRowClick="doGetFieldText" AllowSort="true" PopupMenu="popCopy"
                  NeedRowIndex="true" AllowFrozen="true"
                  DefaultRowCount="1" Pager="Bottom" ReadOnly="false" PagerShowRefreshButton="true"
                  OnSelectionChanged="loadDetails" OnCellBeginEdit="doGridCellBeginEdit"
                  OnRowDblClick="doOrderModify" ShowLazy="${showLazy}">
                <MultiSelectColumn DataField="selected" ShowHeaderCheckBox="true" Width="100" AllowConfig="false"
                                   AllowSort="false" AllowFilter="false" ReportVisible="false" />
                <TextColumn DataField="orderTags" Caption="标记" Width="150" ReadOnly="true" AllowSort="false"
                            ReportVisible="true" AllowHTML="true" OnFilterRendering='doFilterRendering'/>
                <DropDownColumn Caption="网店" DataField="otypeId" ReadOnly="true"
                                DataSource="${eshops}"  AllowStretch="true" MaxWidth="0"
                                DataTextField="fullname" DataValueField="otypeId" DropDownStyle="DropDownSearch"/>
                <TextColumn Caption="订单编号" DataField="tradeOrderId" ReadOnly="true"/>
                <DropDownColumn DataField="localTradeState" DataTextField="name" DataValueField="code"
                                Caption="线上交易状态" ReadOnly="true" Width="100" DropDownStyle="DropDownSearch"
                                DataSource="${tradeStateType}"/>
                <ImageDropDownColumn DataField="sellerFlag" MinWidth="50" Caption="旗帜" ImageHeight="20" ImageWidth="20"
                                     DataSource="${flagsSource}" AllowSort="false" DataTextField="name" ReportVisible="true"
                                     OnChange="onBodyFlagChanged" DataValueField="code" OnFilterRendering='doFilterRendering'/>
                <TextColumn Caption="卖家备注" DataField="sellerMemo" ReadOnly="true"/>
                <TextColumn Caption="买家留言" DataField="buyerMessage" ReadOnly="true"/>
                <TextColumn Caption="发货仓库" DataField="ktypeName" ReadOnly="true"/>
                <DateTimeColumn DataField="timing.planSendTime" Caption="预计发货时间" ReadOnly="true"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <DateTimeColumn DataField="timing.sendTime" Caption="最晚发货时间" ReadOnly="true"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <NumberColumn DataField="disedTaxedTotal" Caption="${disedTaxedTotalCaption}" ReadOnly="true"
                              Width="80"
                              PreFlagDataField="currencycode"  SummaryKind="Sum"
                              DecimalScale="2"/>
                <DateTimeColumn Caption="付款时间" DataField="tradePayTime" ReadOnly="true"/>
                <DropDownColumn DataField="createType" Caption="创建方式" ReadOnly="true" Width="100" DropDownStyle="DropDownSearch"
                                ListItems="0=手工新增,1=下载生成,2=售后生成,16=导入新增" DataTextField="name" DataValueField="code"/>
                <DateTimeColumn Caption="订单创建时间" DataField="createTime" ReadOnly="true"/>


                <TextColumn Caption="平台父级订单号" DataField="platformParentOrderId" ReadOnly="true" Visible="false"/>
                <!--                    <TextColumn Caption="网店" DataField="otypeName" ReadOnly="true"/>-->
                <DropDownColumn ID="ocategory" Caption="网店类型" DataField="ocategory" DropDownStyle="DropDownSearch"
                                ListItems="0=普通网店,1=分销网店,2=代运营网店,3=虚拟店铺" ReadOnly="true" AllowStretch="true" Visible="false"/>
                <DropDownColumn DataField="orderSourceType" Caption="订单类型" ReadOnly="true" Width="100" DropDownStyle="DropDownSearch"
                                DataSource="${orderSourceType}" DataTextField="name" DataValueField="code" Visible="false"/>
                <DropDownColumn Caption="业务类型" DataField="businessType" ReadOnly="true" DropDownStyle="DropDownSearch"
                                DataSource="${businessTypes}" DataTextField="name" DataValueField="key" Visible="false"/>
                <DropDownColumn Caption="发货方" DataField="deliverType" ReadOnly="true" DropDownStyle="DropDownSearch"
                                DataSource="${deliverType}" DataTextField="name" DataValueField="key" Visible="false"/>
                <DateTimeColumn Caption="拍下时间" DataField="tradeCreateTime" ReadOnly="true"  Visible="false"/>
                <DateTimeColumn Caption="交易完成时间" DataField="tradeFinishTime" ReadOnly="true" Visible="false"/>
                <!--                <DateTimeColumn Caption="自提时间" DataField="extend.collectTime" ReadOnly="true" AllowSort="false"/>-->
                <TextColumn DataField="extend.collectCustomer" Caption="自提人" ReadOnly="true" Width="110" AllowConfig="true" Visible="false"/>
                <TextColumn DataField="salesman" Caption="导购员" ReadOnly="true"  Visible="false" Width="110" AllowConfig="true"/>
                <TextColumn DataField="extend.installationServiceProvider" Caption="安装服务商" ReadOnly="true" Width="110" AllowConfig="true"  Visible="false"/>
                <DropDownColumn Caption="支付方式" DataField="extend.paymentMode" DataTextField="name" DropDownStyle="DropDownSearch"
                                DataValueField="code"  Visible="false"
                                DataSource="${paymentModes}" ReadOnly="true" AllowStretch="true"/>
                <TextColumn Caption="旗帜标签" DataField="extend.sellerFlagMemo" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="往来单位" DataField="btypeName" ReadOnly="true"  Visible="false"/>
                <TextColumn Caption="经手人" DataField="etypeName" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="物流公司" DataField="localFreightName" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="物流单号" DataField="localFreightBillNo" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="买家指定物流" DataField="customerExpectedFreightName" Width="150" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="系统备注" DataField="remark" Width="150" ReadOnly="true" Visible="false"/>
                <!--                <TextColumn Caption="平台门店仓库id" DataField="platformStockId" Width="150" ReadOnly="true"/>-->
                <TextColumn Caption="门店ID/网点ID/仓库" DataField="platformStockId" Width="150" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="门店/网点/仓库" DataField="platformStockName" Width="150" ReadOnly="true" Visible="false"/>
                <DropDownColumn DataField="processState" Caption="提交状态" ReadOnly="true" Width="100" DropDownStyle="DropDownSearch"
                                DataSource="${processState}" DataTextField="name" DataValueField="code" Visible="false"/>
                <DropDownColumn DataField="localRefundState" DataTextField="name" DataValueField="code" DropDownStyle="DropDownSearch"
                                Caption="退款状态" ReadOnly="true" Width="100" DataSource="${refundStatus}" Visible="false"/>
                <DropDownColumn DataField="reSendState" DataTextField="name" DataValueField="code" DropDownStyle="DropDownSearch"
                                Caption="换/补状态" ReadOnly="true" Width="100" DataSource="${reSendState}" Visible="false"/>
                <TextColumn DataField="exportState" Caption="导出状态" ReadOnly="true" ReportVisible="false"
                            Width="110" AllowSort="false" AllowConfig="true" OnFilterRendering='doFilterRendering' Visible="false"/>
                <NumberColumn DataField="orderPreferentialAllotTotal" Caption="商家整单优惠金额"
                              ReadOnly="true" Width="80" Visible="false"
                              PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2"/>
                <NumberColumn DataField="platformOrderPreferentialTotal" Caption="平台整单优惠金额"
                              ReadOnly="true" Width="80" PreFlagDataField="currencycode" Visible="false"
                              SummaryKind="Sum" DecimalScale="2"/>
                <NumberColumn DataField="extend.anchorOrderPreferentialTotal" Caption="主播整单优惠金额"
                              ReadOnly="true" Width="80" PreFlagDataField="currencycode"  Visible="false"
                              SummaryKind="Sum" DecimalScale="2"/>
                <NumberColumn DataField="extend.platformOrderSubsidyTotal" Caption="平台补贴商家金额"
                              ReadOnly="true" Width="80" PreFlagDataField="currencycode"  Visible="false"
                              SummaryKind="Sum" DecimalScale="2"/>
                <NumberColumn DataField="orderBuyerFreightFee" Caption="买家运费" ReadOnly="true" Width="80"
                              PreFlagDataField="currencycode"  SummaryKind="Sum" Visible="false"
                              DecimalScale="2"/>
                <NumberColumn DataField="ptypeServiceFee" Caption="服务费" ReadOnly="true" Width="80"
                              PreFlagDataField="currencycode" Visible="false"
                              SummaryKind="Sum" DecimalScale="2"/>
                <NumberColumn DataField="extend.mallDeductionFee" Caption="预估商城扣费金额" ReadOnly="true" Width="80" PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2" AllowFilter = "false" Visible="false"/>
                <NumberColumn DataField="taxTotal" Caption="税额" AllowConfig="${enabledTax}"  Visible="false"
                              ReadOnly="true" Width="80" PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2"/>
                <DateTimeColumn DataField="timing.planSignTime" Caption="预约签收/送达时间" ReadOnly="true" Visible="false"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <DateTimeColumn DataField="timing.promisedSignStartTime" Caption="最早签收时间" ReadOnly="true"
                                Width="110" AllowSort="true" AllowConfig="true"  Visible="false"/>
                <DateTimeColumn DataField="timing.promisedCollectTime" Caption="最晚揽收时间" ReadOnly="true" Visible="false"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <DateTimeColumn DataField="timing.promisedSignTime" Caption="最晚签收时间" ReadOnly="true" Visible="false"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <DateTimeColumn DataField="timing.signTime" Caption="签收时间" ReadOnly="true" Visible="false"
                                Width="110" AllowSort="true" AllowConfig="true"/>
                <CheckBoxColumn TextAlign="Center" Width="80" Caption="是否需要开票" AllowSort="false" Visible="false"
                                DataField="invoiceInfo.invoiceRequired" ReadOnly="true" DisplayNull="false"/>
                <DropDownColumn Caption="线上开票状态" DataField="invoiceInfo.invoiceState" ReadOnly="true" Visible="false"
                                DataSource="${invoiceState}" AllowSort="false" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code"/>
                <TextColumn DataField="eshopBuyer.customerShopAccount" Caption="买家账号" OnDblClick="receiverDbClick"
                            ReadOnly="true" Visible="false"
                            Width="110" AllowConfig="true" AllowSort="false"/>
                <TextColumn DataField="eshopBuyer.fullBuyerInfo" Caption="收货信息" OnDblClick="receiverDbClick"
                            ReadOnly="true" Visible="false"
                            Width="110" AllowSort="false" AllowConfig="true"/>
                <DropDownColumn Caption="交货方式" DataField="selfDeliveryMode" ReadOnly="true" Visible="false"
                                DataSource="${selfDeliveryMode}" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code"/>
                <NumberColumn DataField="buyerTradeTotal" Caption="买家应付金额" ReadOnly="true" Width="80"
                              PreFlagDataField="currencycode" Visible="false"
                              SummaryKind="Sum" DecimalScale="2"/>
                <!--       <NumberColumn DataField="buyerPaidTotal" Caption="买家已付金额" ReadOnly="true" Width="80"
                                     PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                       <NumberColumn DataField="buyerUnpaidTotal" Caption="买家未付金额" ReadOnly="true" Width="80"
                                     PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>-->
                <NumberColumn DataField="distributionDisedTaxedTotal" Caption="${disTaxedTotalCaption}"
                              ReadOnly="true" Width="80" Visible="false"
                              PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2" />
                <NumberColumn DataField="extend.advanceTotal" Caption="预收款" ReadOnly="true" Width="80"
                              PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2" Visible="false"/>
                <TextColumn Caption="发货供应商" DataField="extend.supplierName" ReadOnly="true" Visible="false" AllowSort="false" AllowFilter="false"/>
                <DropDownColumn Caption="收款状态" DataField="extend.gatherStatus" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code" DataSource="${gatherStatus}" ReadOnly="true" AllowStretch="true" Visible="false" />
                <DropDownColumn Caption="订单支付方式" DataField="modeOfPayment" DataTextField="name" DataValueField="code" DataSource="${modeOfPayment}"
                                ReadOnly="true" Visible="false" AllowSort="false" AllowFilter="false"/>
                <TextColumn Caption="商户收款账号" DataField="merchantPaymentAccount" ReadOnly="true" Visible="false" AllowSort="false" AllowFilter="false"/>
                <DropDownColumn Caption="结算类型" DataField="payTimeType" ReadOnly="true"
                                DataSource="${payTimeType}" DropDownStyle="DropDownSearch" Visible="false"
                                DataTextField="name" DataValueField="code"/>
                <TextColumn DataField="extend.groupHeaderName" Caption="团长名称" ReadOnly="true" Visible="false"
                            Width="110" AllowConfig="true"/>
                <DropDownColumn Caption="接单状态" DataField="extend.confirmStatus" ReadOnly="true" Visible="false"
                                DataSource="${platformConfirmStateByOrder}" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code"/>
                <DropDownColumn Caption="配送状态" DataField="extend.logisticsStatus" ReadOnly="true" Visible="false"
                                DataSource="${logisticsState}" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code"/>
                <DropDownColumn Caption="质检状态" DataField="extend.platformQcResult" ReadOnly="true"
                                DataSource="${platformQcResult}" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code" Visible="false" AllowSort="false"/>
                <DropDownColumn Caption="鉴定状态" DataField="extend.platformIdentifyResult" ReadOnly="true"
                                DataSource="${platformIdentifyResult}" DropDownStyle="DropDownSearch"
                                DataTextField="name" DataValueField="code"  Visible="false" AllowSort="false"/>
                <TextColumn DataField="extend.flowChannel" Caption="订单来源" ReadOnly="true"
                            Width="110" AllowConfig="true" Visible="false"/>
                <TextColumn Caption="分销买家订单号" DataField="distribution.distributionBuyerTradeId" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="团名称" DataField="extend.groupTitle" ReadOnly="true" Visible="false"/>
                <DropDownColumn  Caption="草稿状态" DataField="extend.isDraft" DropDownStyle="DropDownSearch" Visible="false"
                                 ListItems="0=正常,1=草稿" ReadOnly="true" AllowStretch="true"/>
                <TextColumn Caption="制单人" DataField="creatorName" ReadOnly="true" Visible="false"/>
                <TextColumn Caption="配送次数" DataField="extend.periodFrequency" ReadOnly="true" Visible="false" AllowSort="false" AllowFilter="false"/>
                <NumberColumn DataField="extend.nationalSubsidyTotal" Caption="国补金额" ReadOnly="true" Width="80"
                              PreFlagDataField="currencycode"
                              SummaryKind="Sum" DecimalScale="2" Visible="false"/>
            </Grid>
            <HSplitter ID="spUpDown" ForUp="c_grid_Audit" ForDown="DownBody" SaveConfig="false" Mode="HideDown" MinHeight="240"/>
            <PageControl CssClass="secondary" Height="240" SelectedTabIndex="0" ID="DownBody">
                <TabPage Caption="商品明细" ID="orderDetails" >
                    <TreeGrid CssClass="detailgrid ExpandFlexGrid" ID="details" Enabled="true"
                              ExpandIDField="id" OnColumnChanged="doColumnChanged"
                              ExpandParentIDField="comboRowId" OnAfterDataBind="doGridAfterDataBind"
                              ExpandRootValue="0" ShowFooter="true" SummaryText="合计" DataField="details"
                              MaxRowCount="100" DefaultRowCount="1" AllowSort="true" AllowCopy="true"
                              WordWrap="false" OnCellBeginEdit="doGridCellBeginEdit">
                        <TextColumn DataField="orderTags" Caption="标记" Width="150" ReadOnly="true"
                                    AllowSort="false" AllowHTML="true"
                                    ReportVisible="false"/>
                        <TextColumn DataField="platformPtypeName" OnGetDisplayText="doGetFullNameText"
                                    Hint="双击可以直接查询网店商品信息" OnDblClick="openUrl" Caption="网店商品名称" AllowHTML="true"
                                    ReadOnly="true" Width="200"/>
                        <TextColumn Caption="网店商品属性" OnDblClick="openUrl" DataField="platformPropertiesName" Width="210"
                                    ReadOnly="true"
                                    Hint="双击可以直接查询网店网店商品信息"/>
                        <TextColumn Caption="网店商品商家编码" Width="200" OnDblClick="openUrl" DataField="platformPtypeXcode"
                                    ReadOnly="true"
                                    Hint="双击可以直接查询网店网店商品信息"/>
                        <ImageColumn Caption="图片" DataField="platformPtypePicUrl" UseResource="false"
                                     ImageHeight="24" ImageWidth="24" Preview='hover' AllowSort="false" ImageUrl=''
                                     ReadOnly="true"/>
                        <ExpandColumn Caption="商品名称" DisplayField="ptypeName" ExpandAll="${expandAll}"
                                      Width="200" OnGetDisplayText="doGetFullNameText"
                                      DataField="ptypeName" ReadOnly="true" MinWidth="100" OnChange='doExpandChange'/>
                        <TextColumn Caption="商品属性" Width="50" DataField="localProperties" Visible="${enabledProps}"
                                    AllowConfig="${enabledProps}" ReadOnly="true"/>
                        <TextColumn Caption="商品单位" DataField="unitName" ReadOnly="true"/>
                        <TextColumn Caption="商家编码" Width="50" DataField="xcode" ReadOnly="true"/>
                        <NumberColumn Caption="数量" DataField="unitQty"
                                      DecimalPrecision="${maxQtyLen}"
                                      DecimalScale="${qtyLength}" ReadOnly="true"
                                      DefaultValue="1"/>
                        <NumberColumn DataField="disedTaxedPrice" Caption="${disedTaxedPriceCaption}"
                                      ReadOnly="true" Width="80"
                                      PreFlagDataField="currencycode"
                                      DecimalScale="4"/>
                        <NumberColumn DataField="disedTaxedTotal" Caption="${disedTaxedTotalCaption}"
                                      ReadOnly="true" Width="80"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <TextColumn Caption="明细备注" DataField="remark" ReadOnly="true" MaxLength="100"/>
                        <DropDownColumn DataField="processState" Caption="提交状态" ReadOnly="true" Width="100" Visible="false"
                                        DataSource="${detailProcessState}" DataTextField="name" DataValueField="code"/>
                        <TextColumn Caption="子订单号" Width="200"  DataField="tradeOrderDetailId" Visible="false"
                                    ReadOnly="true"/>
                        <TextColumn Caption="商品简名" DataField="ptypeShortName" ReadOnly="true" Visible="false"/>
                        <SelectorColumn ID="ptypeCode" DataField="ptypeCode" Caption="商品/套餐编号" Visible="false"
                                        LangID="ptypeCode" NullDisplayText="输入商品编号/名称/sku编号搜索"
                                        SelectorHeight="500" SelectorWidth="1000"
                                        ShowMDI='true'
                                        OnButtonClick="doPtypeColumnButtonClick"
                                        Width="200"
                                        Business="{'Name':'jxc.ptypeColumnNew', 'PassEvent':true, 'selectType':'Sku','showOnlyBaseunit':'false','handleRemove':false}"/>
                        <TextColumn Caption="属性格式" Width="50" DataField="localPropertiesName" Visible="false"
                                    AllowConfig="${enabledProps}" ReadOnly="true"/>
                        <TextColumn Caption="条码" Width="50" DataField="barcode" ReadOnly="true" Visible="false"/>
                        <NumberColumn Caption="副单位数量" DataField="subQty"  OnGetDisplayText="initComboRowSubQty"
                                      DecimalScale="${qtyLength}" ReadOnly="true" AllowConfig="${enableSub}" Visible="false"/>
                        <!--                        <TextColumn Caption="副单位名" DataField="subUnit" ReadOnly="true" AllowConfig="${enableSub}" Visible="${enableSub}"/>-->
                        <TextColumn Caption="单位关系" DataField="unitRelation" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="辅助数量" DataField="unitRateName" ReadOnly="true" Visible="false"/>
                        <NumberColumn DataField="price" Caption="平台单价"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      DecimalScale="4"/>
                        <NumberColumn DataField="total" Caption="平台金额"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="ptypePreferentialTotal" Caption="商家单品优惠金额"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="platformPtypePreferentialTotal" Caption="平台单品优惠金额"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="extend.anchorOrderPreferentialTotal" Caption="主播整单优惠分摊"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="extend.anchorPtypePreferentialTotal" Caption="主播单品优惠金额"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="extend.platformPtypeSubsidyTotal" Caption="平台补贴商家单品金额"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="extend.platformOrderSubsidyTotal" Caption="平台补贴商家整单金额分摊"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="orderPreferentialAllotTotal" Caption="商家整单优惠分摊"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn DataField="platformOrderPreferentialTotal" Caption="平台整单优惠分摊"
                                      ReadOnly="true" Width="80" Visible="false"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                        <NumberColumn Caption="服务费" DataField="ptypeServiceFee" ReadOnly="true" SummaryKind="Sum"
                                      Width="80" PreFlagDataField="currencycode" Visible="false"
                                      DecimalScale="${totalLength}" DecimalPrecision="${maxTotalLen}"/>
                        <NumberColumn Caption="预估商城扣费金额" DataField="extend.mallDeductionFee" ReadOnly="true" SummaryKind="Sum"
                                      Width="80" PreFlagDataField="currencycode" Visible="false"
                                      DecimalScale="${totalLength}" DecimalPrecision="${maxTotalLen}"/>
                        <NumberColumn Caption="商家折扣" ReadOnly="true" Visible="false"
                                      DataField="discount" DecimalScale="4" Hint="0.9为9折"/>
                        <NumberColumn Caption="税率(%)" DataField="taxRate" DecimalScale="${taxLength}"
                                      MaxValue="100" Visible="false" ReadOnly="true"
                                      AllowConfig="${enabledTax}"/>
                        <NumberColumn Caption="税额"  DataField="taxTotal" ReadOnly="true"
                                      DecimalScale="${totalLength}"
                                      DecimalPrecision="${maxTotalLen}" SummaryKind="Sum" Visible="false"
                                      AllowConfig="${enabledTax}"/>
                        <TextColumn Caption="规格" DataField="ptypeStandard" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="型号" DataField="ptypeType" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="产地" DataField="ptypeArea" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="商品备注" DataField="ptypeMemo" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="品牌" DataField="brandName" ReadOnly="true" Visible="false"/>
                        <DropDownColumn Caption="交易类型" DataField="orderSaleType" ReadOnly="true" Visible="false"
                                        DataSource="${orderSaleType}" AllowSort="false"
                                        DataTextField="name" DataValueField="code"/>
                        <DropDownColumn DataField="platformDetailTradeState" DataTextField="name" DataValueField="code"
                                        Caption="线上交易状态" ReadOnly="true" Width="100" Visible="false"
                                        DataSource="${tradeStateType}"/>
                        <DropDownColumn DataField="localRefundState" DataTextField="name" DataValueField="code" Visible="false"
                                        Caption="退款状态" ReadOnly="true" Width="100" DataSource="${refundStatus}"/>
                        <DropDownColumn DataField="reSendState" DataTextField="name" DataValueField="code" DropDownStyle="DropDownSearch" Visible="false"
                                        Caption="换/补状态" ReadOnly="true" Width="100" DataSource="${reSendState}"/>
                        <!--  <NumberColumn DataField="distributionCommissionTotal" Caption="分佣金额	" ReadOnly="true" Width="80"
                                        PreFlagDataField="currencycode"
                                         SummaryKind="Sum" DecimalScale="2"/>-->
                        <NumberColumn DataField="distribution.buyerDisedTaxedPrice" Caption="${disTaxedPriceCaption}" Visible="false"
                                      ReadOnly="true" Width="80" DecimalScale="${priceLength}" DecimalPrecision="${maxTotalLen}"
                                      PreFlagDataField="currencycode"
                        />
                        <NumberColumn DataField="distribution.buyerDisedTaxedTotal" Caption="${disTaxedTotalCaption}" Visible="false"
                                      ReadOnly="true" Width="80"  DecimalScale="${totalLength}" DecimalPrecision="${maxTotalLen}"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum"/>
                        <NumberColumn DataField="purchase.purchasePrice" Caption="采购单价"
                                      ReadOnly="true" Width="80"  DecimalScale="${priceLength}" DecimalPrecision="${maxTotalLen}"
                                      PreFlagDataField="currencycode"
                                      Visible="false"/>
                        <NumberColumn DataField="purchase.purchaseTotal" Caption="采购金额"
                                      ReadOnly="true" Width="80" DecimalScale="${totalLength}" DecimalPrecision="${maxTotalLen}"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" Visible="false"/>
                        <TextColumn Caption="主播id" DataField="liveBroadcast.platformAnchorId" ReadOnly="true" MaxLength="100" Visible="false"/>
                        <TextColumn Caption="主播名称" DataField="liveBroadcast.platformAnchorName" ReadOnly="true" MaxLength="100" Visible="false"/>
                        <TextColumn Caption="直播间id" DataField="liveBroadcast.platformLiveRoomId" ReadOnly="true" MaxLength="100" Visible="false"/>
                        <TextColumn Caption="批次号" DataField="batch.batchno" ReadOnly="true" MaxLength="100"  AllowConfig="${showBatch}" Visible="false"/>
                        <DateColumn Caption="生产日期" DataField="batch.produceDate" ReadOnly="true" MaxLength="100" AllowConfig="${showBatch}" Visible="false"/>
                        <DateColumn Caption="到期日期" DataField="batch.expireDate" ReadOnly="true" MaxLength="100" AllowConfig="${showBatch}" Visible="false"/>
                        <TextColumn Caption="定制信息" DataField="displayCustomInfo" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="鉴定码" DataField="verifyCode" ReadOnly="true" Visible="false"/>
                        <DropDownColumn Caption="质检状态" DataField="platformQcResult" ReadOnly="true"
                                        DataSource="${platformQcResult}" DropDownStyle="DropDownSearch"
                                        DataTextField="name" DataValueField="code" Visible="false"/>
                        <TextColumn Caption="质检说明" DataField="platformQcResultDesc" ReadOnly="true" Visible="false"/>
                        <DropDownColumn Caption="鉴定状态" DataField="platformIdentifyResult" ReadOnly="true"
                                        DataSource="${platformIdentifyResult}" DropDownStyle="DropDownSearch"
                                        DataTextField="name" DataValueField="code"  Visible="false"/>
                        <TextColumn Caption="鉴定说明" DataField="platformIdentifyResultDesc" ReadOnly="true" Visible="false"/>
                        <TextColumn DataField="flowChannel" Caption="订单来源" ReadOnly="true"
                                    Width="110" AllowConfig="true" Visible="false"/>
                        <TextColumn Caption="分销买家订单号" DataField="distribution.distributionBuyerTradeDetailId" ReadOnly="true" Visible="false"/>
                        <DateTimeColumn DataField="timing.promisedSendTime" Caption="最晚发货时间" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="门店ID/网点ID/仓库" DataField="platformStockId" Width="150" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="门店/网点/仓库" DataField="platformStockName" Width="150" ReadOnly="true" Visible="false"/>
                        <TextColumn Caption="发货仓库" DataField="ktypeName" ReadOnly="true" Visible="false"/>
                        <NumberColumn DataField="extend.nationalSubsidyTotal" Caption="国补金额" ReadOnly="true" Width="80"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2" Visible="false"/>
                    </TreeGrid>
                </TabPage>
                <TabPage CssStyle="border-top: 1px solid #e1e1e1;" Caption="收货信息" ID="receiverInfo">

                    <Label Text='收货信息' Tag="BaseInfoTopic" ID="receiverInfoL" CssClass='LayoutGroupCaption'/>
                    <FlexBlock ID="receiverInfoB">
                        <FlowPanel  ColSpan="3" CssClass='FlowTable' ItemLabelWidth="120" ID="ReceiverInfoFlowPanel" CssStyle="width:80%;">
                            <TextEdit Label="收货人姓名：" ID="edReceiver" DataField="customerReceiver"
                                      Enabled="false"
                                      MaxLength="25" ReportVisible="false"/>
                            <TextEdit Label="收货人手机：" ID="edMobile" DataField="customerReceiverMobile"
                                      Enabled="false"
                                      MaxLength="22" ReportVisible="false"/>
                            <TextEdit Label="收货人电话：" ID="edPhone" DataField="customerReceiverPhone"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                            <TextEdit Label="收货地址：" Enabled="false" ID="address" DataField="customerReceiverFullAddress"
                                      ReadOnly="true"
                                      ReportVisible="false"/>
                            <TextEdit Label="邮编：" ID="edZipcode" DataField="customerReceiverZipCode"
                                      Enabled="false" MaxByteLength="50" ReportVisible="false"/>
                            <TextEdit Label="邮箱：" ID="edEmail" DataField="customerEmail"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                        </FlowPanel>
                        <Block CssClass='Flex1'/>
                        <FlowPanel LayoutDirection="Vert">
                            <Button Text="复制全部" CssStyle="margin-right: 8px;" OnClick="doCopyReceiveInfo"  Visible="eshoporder.eshopsaleorder.copyBuyerInfo"/>
                            <Button Text="显示明文" CssStyle="margin-right: 8px;" OnClick="doShowReceiveInfo" Visible="sale.buyer.cryptograph.view"/>
                        </FlowPanel>
                    </FlexBlock>


                    <Label Text='买家信息' Tag="BaseInfoTopic" ID="realBuyerL" CssClass='LayoutGroupCaption'/>
                    <FlexBlock ID="realBuyerB">
                        <FlowPanel  ColSpan="3" CssClass='FlowTable' ItemLabelWidth="120" ID="realBuyerFlowPanel" CssStyle="width:80%;">
                            <TextEdit Label="买家姓名：" ID="realBuyerReceiver" DataField="customerReceiver"
                                      Enabled="false"
                                      MaxLength="25" ReportVisible="false"/>
                            <TextEdit Label="买家手机：" ID="realBuyerMobile" DataField="customerReceiverMobile"
                                      Enabled="false"
                                      MaxLength="22" ReportVisible="false"/>
                            <TextEdit Label="买家电话：" ID="realBuyerPhone" DataField="customerReceiverPhone"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                            <TextEdit Label="收货地址：" Enabled="false" ID="realBuyerAddress" DataField="customerReceiverFullAddress"
                                      ReadOnly="true"
                                      ReportVisible="false"/>
                            <TextEdit Label="邮编：" ID="realBuyerZipcode" DataField="customerReceiverZipCode"
                                      Enabled="false" MaxByteLength="50" ReportVisible="false"/>
                            <TextEdit Label="邮箱：" ID="realBuyerEmail" DataField="customerEmail"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                        </FlowPanel>
                        <Block CssClass='Flex1'/>
                        <FlowPanel LayoutDirection="Vert">
                            <Button Text="复制全部" CssStyle="margin-right: 8px;" OnClick="doCopyRealBuyerInfo"  Visible="eshoporder.eshopsaleorder.copyBuyerInfo"/>
                            <Button Text="显示明文" CssStyle="margin-right: 8px;" OnClick="doShowRealBuyerInfo" Visible="sale.buyer.cryptograph.view"/>
                        </FlowPanel>
                    </FlexBlock>

                    <Label Text='自提点信息' Tag="BaseInfoTopic" ID="selfPickUpL" CssClass='LayoutGroupCaption'/>
                    <FlexBlock ID="selfPickUpB">
                        <FlowPanel  ColSpan="3" CssClass='FlowTable' ItemLabelWidth="120" ID="selfPickUpFlowPanel" CssStyle="width:80%;">
                            <TextEdit Label="自提点名称：" ID="selfPickUpShopAccount" DataField="customerShopAccount"
                                      Enabled="false"
                                      MaxLength="25" ReportVisible="false"/>
                            <TextEdit Label="联系人姓名：" ID="selfPickUpReceiver" DataField="customerReceiver"
                                      Enabled="false"
                                      MaxLength="22" ReportVisible="false"/>
                            <TextEdit Label="联系人手机：" ID="selfPickUpMobile" DataField="customerReceiverMobile"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                            <TextEdit Label="联系人电话：" Enabled="false" ID="selfPickUpPhone" DataField="customerReceiverPhone"
                                      ReadOnly="true"
                                      ReportVisible="false"/>
                            <TextEdit Label="邮编：" ID="selfPickUpZipcode" DataField="customerReceiverZipCode"
                                      Enabled="false" MaxByteLength="50" ReportVisible="false"/>
                            <TextEdit Label="邮箱：" ID="selfPickUpEmail" DataField="customerEmail"
                                      Enabled="false"
                                      MaxLength="30" ReportVisible="false"/>
                            <TextEdit Label="收货地址：" Enabled="false" ID="selfPickUpAddress" DataField="customerReceiverFullAddress"
                                      ReadOnly="true"
                                      ReportVisible="false"/>
                        </FlowPanel>
                        <Block CssClass='Flex1'/>
                        <FlowPanel LayoutDirection="Vert">
                            <Button Text="复制全部" CssStyle="margin-right: 8px;" OnClick="doCopySelfPickUpInfo" Visible="eshoporder.eshopsaleorder.copyBuyerInfo"/>
                            <Button Text="显示明文" CssStyle="margin-right: 8px;" OnClick="doShowSelfPickUpInfo" Visible="sale.buyer.cryptograph.view"/>
                        </FlowPanel>
                    </FlexBlock>
                </TabPage>
                <TabPage Caption="物流信息">
                    <Grid ID="c_grid_freight" AutoPagerPageSize="false" PagerAutoFocus="false"
                          AllowSort="true" AllowCopy="true" Enabled="true"
                          MaxRowCount="100" DefaultRowCount="1" NeedRowIndex="true">
                        <TextColumn DataField="freightName" Caption="物流公司" ReadOnly="true" AllowStretch='true'/>
                        <TextColumn DataField="freightNo" Caption="物流单号" ReadOnly="true" AllowStretch='true'/>
                        <DropDownColumn DataField="freightInterceptStatus" Caption="拦截状态" ReadOnly="true" Width="700"
                                        DataSource="${freightInterceptStatus}" DataTextField="name" DataValueField="code" AllowStretch='true'/>
                    </Grid>
                </TabPage>
                <TabPage CssStyle="border-top: 1px solid #e1e1e1;" Caption="发票信息">
                    <FlowPanel ItemWidth="350" ItemLabelWidth="120" CssClass='FlowTable'
                               ID="invoiceInfoFlowPanel">
                        <DropDownEdit Label="是否需要开票：" ReportVisible="false" Enabled="false" ListItems="0=否,1=是"
                                      DataField="invoiceInfo.invoiceRequired"
                                      ID="needInvoiceControl"
                                      DropDownStyle="DropDownList"/>
                        <DropDownEdit Label="线上开票状态：" ReportVisible="false" Enabled="false" DataSource="${invoiceState}"
                                      Tag="setVisible"
                                      DropDownStyle="DropDownList"
                                      DataTextField="name" DataValueField="code"
                                      DataField="invoiceInfo.invoiceState"/>
                        <DropDownEdit Label="发票类型：" ReportVisible="false" Enabled="false"
                                      DataSource="${invoiceCategory}"
                                      DataTextField="name"
                                      DropDownStyle="DropDownList"
                                      DataValueField="code" DataField="invoiceInfo.invoiceCategory" Tag="setVisible"/>
                        <Block CssClass='clear'/>
                        <DropDownEdit Label="开票单位：" ReportVisible="false" Enabled="false" ListItems="0=个人,1=企业"
                                      DataField="invoiceInfo.invoiceType" Tag="setVisible"
                                      DropDownStyle="DropDownList"/>
                        <TextEdit Label="发票抬头：" ReportVisible="false" Enabled="false"
                                  DataField="invoiceInfo.invoiceTitle"
                        />
                        <TextEdit ID="edInvoiceSpecialCode" Label="纳税人识别号：" ReportVisible="false" Enabled="false"
                                  DataField="invoiceInfo.invoiceCode"
                        />
                        <Block CssClass='clear'/>
                        <TextEdit ID="edInvoiceBank" Label="开户银行：" DataField="invoiceInfo.invoiceBank" Tag="setVisible" MaxLength="200"
                                  ReportVisible="false" Enabled="false"/>
                        <TextEdit ID="edInvoiceBankAccount" Label="开户账号：" DataField="invoiceInfo.invoiceBankAccount" Tag="setVisible"
                                  MaxLength="200"
                                  ReportVisible="false" Enabled="false"/>
                        <TextEdit ID="edInvoiceRegisterPhone"  Label="注册电话：" DataField="invoiceInfo.invoiceRegisterPhone" Tag="setVisible"
                                  MaxLength="200"
                                  ReportVisible="false" Enabled="false"/>
                        <Block CssClass='clear'/>
                        <TextEdit ID="edInvoiceRegisterAddr" Label="注册地址：" DataField="invoiceInfo.invoiceRegisterAddr" Tag="setVisible"
                                  MaxLength="200"
                                  ReportVisible="false" Enabled="false"/>
                        <TextEdit ID="edInvoiceRemark" Label="发票备注：" ReportVisible="false" Enabled="false"
                                  DataField="invoiceInfo.invoiceRemark"/>
                    </FlowPanel>
                    <FlowPanel ItemWidth="350" Visible="false" ItemLabelWidth="120" CssClass='FlowTable'
                               ID="notRequired">
                        <DropDownEdit ID="needInvoiceNotRequired" Label="是否需要开票：" SelectedIndex="0"  ReportVisible="false" Enabled="false"
                                      ListItems="0=否,1=是"
                                      DropDownStyle="DropDownList"/>
                        <Button ID="showInvoice" Text="查看平台发票信息" OnClick="doShowInvoice" ReportVisible="false" Visible="false"/>
                    </FlowPanel>
                    <HPanel HAlign="Center">
                        <Button ID="showInvoiceInfo"  Text="显示明文" OnClick="doShowInvoiceInfo"/>
                        <Button ID="resetInvoiceInfo"  Text="取消" OnClick="resetInvoice"/>
                    </HPanel>
                    <VSpacer Height="20"/>
                </TabPage>
                <TabPage Caption="关联单据">
                    <Grid ID="c_gridDelivers" AutoPagerPageSize="false" PagerAutoFocus="false"
                          AllowSort="true" AllowCopy="true" Enabled="true"
                          MaxRowCount="100" DefaultRowCount="1" NeedRowIndex="true">
                        <TextColumn DataField="tradeOrderId" Caption="订单编号" ReadOnly="true" Width="100"/>
                        <TextColumn DataField="taskNumber" Caption="任务单号" ReadOnly="true" Width="100"/>
                        <TextColumn DataField="fullLinkLinkShow" Caption="系统处理状态" ReadOnly="true" Width="100"/>
                        <TextColumn DataField="freightBTypeName" Caption="物流公司" ReadOnly="true"/>
                        <TextColumn DataField="freightNumber" Caption="物流单号" ReadOnly="true"/>
                        <TextColumn DataField="billNumber" Caption="收款单号" ReadOnly="true" />
                        <NumberColumn DataField="billTotal" Caption="金额" ReadOnly="true" Width="80"
                                      PreFlagDataField="currencycode"
                                      SummaryKind="Sum" DecimalScale="2"/>
                    </Grid>
                </TabPage>
                <TabPage Caption="订单日志">
                    <Grid ID="c_gridLogs" AllowSort="true" ReadOnly="true" WordWrap="true" AllowFilter="true"  OnFilter="doOrderLogFilter">
                        <DateTimeColumn DataField="opreateTime" Caption="操作时间" HeaderAlign="Center" ReadOnly="true" Width='130' MinWidth='130'/>
                        <TextColumn DataField="comment" Caption="操作内容" HeaderAlign="Center" ReadOnly="true"
                                    AllowStretch='true'/>
                        <DropDownColumn DataField="processState" Caption="提交状态" ReadOnly="true"
                                        DataSource="${processState}" DataTextField="name" DataValueField="code"/>
                        <TextColumn DataField="etypeName" Caption="操作员" HeaderAlign="Center" ReadOnly="true"/>
                    </Grid>
                </TabPage>
                <TabPage CssStyle="border-top: 1px solid #e1e1e1;" Caption="配送信息"  ID="deliveryInfoFlowPanel">
                    <FlowPanel ItemWidth="350" ItemLabelWidth="120" CssClass='FlowTable'>
                        <TextEdit ID="edDeliveryPickupCode" Label="提货码：" ReportVisible="false" Enabled="false"
                                  DataField="pickupCode"/>
                        <TextEdit ID="edDeliveryTakeGoodsCode" Label="收货人取件码：" ReportVisible="false" Enabled="false"
                                  DataField="takeGoodsCode"/>
                    </FlowPanel>
                    <FlowPanel ItemWidth="350" ItemLabelWidth="120" CssClass='FlowTable'>
                        <TextEdit ID="edDeliveryPlatformDispatcherName" Label="骑手名字：" ReportVisible="false" Enabled="false"
                                  DataField="platformDispatcherName"/>
                        <TextEdit ID="edDeliveryPlatformDispatherMobile" Label="骑手手机：" ReportVisible="false" Enabled="false"
                                  DataField="platformDispatherMobile"/>
                    </FlowPanel>
                    <VSpacer Height="20"/>
                </TabPage>
                <TabPage Caption="质检" ID="qualityFlowPanel">
                    <FlowPanel ItemWidth="350" ItemLabelWidth="120" CssClass='FlowTable'>
                        <!--                        <DropDownColumn DataField="qualityMode" Caption="质检方式" ReadOnly="true" Width="700"-->
                        <!--                                        DataSource="${qualityMode}" DataTextField="name" DataValueField="code" AllowStretch='true'/>-->
                        <TextEdit ID="platformQualityOrgName" Label="质检机构：" ReportVisible="true" Enabled="false"
                                  DataField="platformQualityOrgName"/>
                        <TextEdit Label="质检机构收货地址：" Enabled="false" ID="qualityAddress" DataField="customerReceiverFullAddress"
                                  ReadOnly="true"
                                  ReportVisible="false"/>
                    </FlowPanel>
                    <FlowPanel ItemWidth="350" ItemLabelWidth="120" CssClass='FlowTable'>
                    </FlowPanel>
                </TabPage>
                <TabPage Caption="配送周期" ID="cyclePurchaseFlowPanel">
                    <Grid ID="cyclePurchaseGird" AllowSort="false" ReadOnly="true" WordWrap="true" AllowFilter="false"  >
                        <TextColumn DataField="periodFrequency" Caption="配送次数" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <DateTimeColumn DataField="planSignTime" Caption="预约签收/送达时间" HeaderAlign="Center" ReadOnly="true" Width='130' MinWidth='130' TextAlign="Center"/>
                        <DateTimeColumn DataField="planSendTime" Caption="预计发货时间" HeaderAlign="Center" ReadOnly="true" Width='130' MinWidth='130' TextAlign="Center"/>
                        <TextColumn DataField="localTradeState" Caption="线上交易状态" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <TextColumn DataField="localRefundState" Caption="退款状态" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <TextColumn DataField="fullLinkLinkShow" Caption="系统处理状态" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <TextColumn DataField="freightBTypeName" Caption="物流公司" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <TextColumn DataField="freightNumber" Caption="物流单号" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <TextColumn DataField="taskNumber" Caption="任务单号" HeaderAlign="Center" ReadOnly="true" AllowStretch='true' TextAlign="Center"/>
                        <DateTimeColumn DataField="sendTime" Caption="系统发货时间" HeaderAlign="Center" ReadOnly="true" Width='130' MinWidth='130' TextAlign="Center"/>
                    </Grid>
                </TabPage>


            </PageControl>
        </FlexColumn>
    </FlexBlock>

    <PopupBlock ID='SubmitConfigView' CssClass="htmlTipBlock" Width='600' >
        <FlexColumn CssClass="htmlTip">
            <Label Text='提交说明' CssStyle='font-weight:bold;font-size:14px;' />
            <Label CssClass='p' Text='平台普通订单默认自动提交，手工提交用于自动生单工具出错情况下的补漏。' />
            <Label CssClass='p' AllowHTML="true" Text='${submitShowHtml}' Height="50"/>
            <Label CssClass='p' AllowHTML="true" Text='${autoSubmitShowHtml}'  Height="50"/>
            <Label CssClass='p' AllowHTML="true" Text='${platformSendShowHtml}'  Height="50"/>
            <Label CssClass='p' Text="“预售订单”自动提交到“预售订单处理”，后续提交订单审核可以到“预售订单处理”处理。" />
            <VBlock CssClass="bg2">
                <Label CssStyle="width:100%" Text='特殊情况：' />
                <Label CssStyle="margin-left:25px;" Text="一个订单是“预售+现货订单”，会拆分成一个“预售订单”，一个“现货订单”。" />
                <Label CssStyle="margin-left:25px;" Text="（现货订单直接进入订单审核，预售订单先进入预售订单处理，等待提交）" />
            </VBlock>
        </FlexColumn>
    </PopupBlock>
    <PopupMenu ID='menu2'>
        <MenuItem Text='修改卖家备注' Enabled="eshoporder.eshopsaleorder.updateBuyerMessage" OnClick='doModifySellerMessage'/>
        <MenuItem Text='修改系统备注' Enabled="eshoporder.eshopsaleorder.updateRemark" OnClick="doModifyRemark"/>
        <MenuItem Text='关闭交易' Enabled="eshoporder.eshopsaleorder.closeOrder" OnClick="doCloseOrder"/>
    </PopupMenu>
    <PopupMenu ID='menu3'>
        <MenuItem Text='导出单据' OnClick='doExport'/>
        <MenuItem Text='导出记录' OnClick="doExportList"/>
    </PopupMenu>
    <PopupMenu ID='menu4'>
        <!--        <MenuItem Text="导入订单" OnClick="doShowImport" Visible="eshoporder.eshopImportOrderPage.view"/>-->
        <!--        <MenuItem Text="下载订单" OnClick="doDownload" Visible="eshoporder.eshopsaleorder.downloadOrder"/>-->
        <MenuItem Text="更新订单" OnClick="updateOrders" Visible="eshoporder.eshopsaleorder.updateOrderAllData" Param="5"/>
        <MenuItem Text="删除订单" OnClick="deleteOrderBatch" Visible="eshoporder.eshopsaleorder.deleteOrRecoveryOrder" />
        <MenuItem Text="撤销删除" OnClick="deleteRecoveryBatch" Visible="eshoporder.eshopsaleorder.deleteOrRecoveryOrder"/>
    </PopupMenu>
    <PopupMenu ID='submitConfigPopMenu'>
        <MenuItem Text="自动提交设置" OnClick="doShowEshopOrderAutoSubmitConfig" />
    </PopupMenu>
    <PopupMenu ID="popCopy" OnPopup="onPop">
        <MenuItem ID="btnViewEshopOrder" Text="去网店后台看订单" Enabled="eshoporder.eshopsaleorder.viewOnlineOrder"
                  OnClick="doShowEShopOrder"/>
        <MenuItem ID="popCopyItem" Text="复制该字段" Enabled="eshoporder.eshopsaleorder.copy" OnClick="doCopy"/>
        <MenuItem ID="btnBindBtype" Text="客户对应" OnClick="doShowBindBtype" Enabled="eshoporder.platformBtypeMap.view"/>
        <MenuItem Text="重新下载" ID="updateEditAllData" Param="5"
                  Enabled="eshoporder.eshopsaleorder.updateOrderAllData"
                  OnClick="doUpdateOneBill"/>
        <MenuItem ID="btnEdit" Text="编辑" Enabled="eshoporder.eshopEditOrderPage.view" OnClick="doEdit"/>
        <MenuItem ID="deleteOrder" Text="删除订单" OnClick="deleteOrder" Visible="${deleteOrRecoveryOrder}"/>
        <MenuItem ID="deleteRecovery" Text="删除恢复" OnClick="deleteRecovery" Visible="${deleteOrRecoveryOrder}"/>
<!--        <MenuItem ID="popBatchCopyTradeOrderId" Text="批量复制订单编号" Enabled="eshoporder.eshopsaleorder.copy" OnClick="doBatchCopyTradeOrderId"/>-->
<!--        <MenuItem ID="popBatchCopyDeliverBillNo" Text="批量复制物流单号" Enabled="eshoporder.eshopsaleorder.copy" OnClick="doBatchCopyDeliverBillNo"/>-->
        <!--<MenuItem ID="popCopyAdd" Text="复制新增订单" OnClick="doCopyAdd"/>-->
        <!--<MenuItem ID="btnViewOrderLog" Text="原单日志查询" OnClick="doQueryOrderLog"/>-->
        <!--  <MenuItem ID="btnCopyBuyer" Text="复制收货人信息" OnClick="doCopyBuyer" />
          <MenuItem Visible="${EditMessage}" Text="修改卖家备注" OnClick="doModifySellerMessage" />
          <MenuItem ID="btnUpdateBill" Visible="${UpdateEShopOrder}" Text="更新订单" OnClick="doUpdateOneBill" />
          <MenuItem Text="添加订单日志" OnClick="doAddLog" Visible="${AddOrderLog}" />
          <MenuItem ID="btnQueryOrderLog" Visible="${ViewOrderLog}" Text="查看订单日志" OnClick="doQueryOrderLog" />
          <MenuItem ID="btnViewEshopOrderView" Visible="${ViewOrderDetails}" Text="查看订单详情" OnClick="doShowEShopSaleOrderView" />-->
    </PopupMenu>
    <PopupMenu ID='menu5'>
        <MenuItem Text="导入收款" OnClick="doImportGather"/>
    </PopupMenu>
    <PopupBlock ID="saveQuickQueryMenu" CssStyle="z-index:999;overflow:auto;padding: 15px 15px;border-width: 0px" Width="250" Height="128">
        <VPanel>
            <TextEdit ID="customerQueryTitle" Width="210" Height="30" MaxLength="60" NullDisplayText="请输入自定义名称"/>
            <HPanel HAlign="Right" CssStyle="margin-top: 31px">
                <Button Text="保存" OnClick="saveCustomerQuery" CssClass='SpecialButton'/>
                <Button Text="取消" OnClick="cancelCustomerQuery"/>
            </HPanel>
        </VPanel>
    </PopupBlock>
    <PopupBlock ID="updatePop" CssStyle="padding:0;display:flex;flex-direction:column;" >
        <HiddenField ID="quickQueryId"/>
        <FlowPanel LayoutDirection="Vert" CssClass='EShopPage oauto FlexShrink1 VertItem margin0 pd10' ID="updateCustomerQueryPanel">
            <Label Text="自定义查询名称:"/>
            <TextEdit ID="updateQueryName" MaxLength="60" Width="220"/>
            <DropDownEdit Label="时间类型" ReportField="时间类型" SelectedIndex="1" DropDownStyle="DropDownList"
                          ID="updateTimeType" DataField="timeType" ReportVisible="false"
                          DataSource="${queryTimeType}" CssClass='FlexAuto' DataTextField="name"
                          DataValueField="code"/>
            <DropDownCheckBoxList ID="updateEshop" Label="网店" ReportField="网店" DataField="otypeId"
                                  DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                  DataSource="${eshops}"
                                  DataTextField="fullname" DataValueField="otypeId"
                                  LayoutDirection="Vert"/>
            <DropDownCheckBoxList Label="创建方式" ID="updatecreateType" DropDownStyle="DropDownList"
                                  DataField="createType"
                                  ReportField="创建方式" ReportVisible="true"
                                  DataTextField="name" DataValueField="code" DataSource="${createType}"
                                  CssClass='FlexAuto'/>
            <DropDownCheckBoxList Label="业务类型" ID="updateBusinessType" DropDownStyle="DropDownList"
                                  DataField="businessType"
                                  DataTextField="name" DataValueField="code" DataSource="${businessTypes}"
                                  CssClass='FlexAuto'/>
            <NavBar CssClass="updateNavBox">
                <NavGroup Text="订单状态" Expanded="true" GroupName="GroupA">
                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                        <DropDownCheckBoxList Label="线上交易状态:" ReportField="线上交易状态" ID="updateTradeStatus"
                                              DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                              Width="148" DataField="tradeStatus"
                                              DataSource="${tradeStateType}"
                                              DataTextField="name" DataValueField="code"
                                              LayoutDirection="Vert"/>
                        <DropDownCheckBoxList Label="退款状态:" ReportField="退款状态" ID="updateRefundState"
                                              DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                              Width="148" DataField="localRefundProcessState"
                                              LayoutDirection="Vert"
                                              DataSource="${refundStatus}" DataTextField="name"
                                              DataValueField="code"/>
                        <DropDownCheckBoxList Label="删除状态:" ReportField="删除状态" ID="updateDeleteState" ReportVisible="true"
                                              DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                              Width="148" DataField="deleteState"
                                              LayoutDirection="Vert"
                                              DataSource="${deleteState}" DataTextField="name"
                                              DataValueField="code"/>
                        <DropDownCheckBoxList Label="导出状态:" ReportField="导出状态" ID="updateExportState" ReportVisible="true"
                                              DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                              Width="148" DataField="exportState"
                                              LayoutDirection="Vert"
                                              ListItems="0=未导出,1=已导出" DataTextField="name"
                                              DataValueField="code"/>
                        <DropDownCheckBoxList Label="草稿状态:" ReportField="草稿状态" ID="updateDraftState" ReportVisible="true"
                                              DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                              Width="148" DataField="draftState"
                                              LayoutDirection="Vert"
                                              ListItems="0=正常,1=草稿" DataTextField="name"
                                              DataValueField="code"/>
                    </FlowPanel>
                </NavGroup>
            </NavBar>
            <NavBar CssClass="updateNavBox">
                <NavGroup Text="标记/旗帜" Expanded="false" GroupName="GroupB">
                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                        <HPanel LayoutDirection='Horz'>
                            <Label Text="标记:"/>
                            <DropDownEdit ID="updateMarkCondition"  DataField="updateMarkCondition" ListItems='0=包含,1=不包含,2=仅包含'
                                          Value='0'  Width="80" Tag="updateMarkCondition" DropDownStyle="DropDownList"/>
                        </HPanel>
                        <DropDownCheckBoxList ReportField="标记" ID="updateMark"
                                              DropDownStyle="DropDownSearch"
                                              CssClass='FlexAuto'
                                              Width="148" DataField="mentionValues"
                                              LayoutDirection="Vert"
                                              DataSource="${markTypeList}" DataTextField="name"
                                              DataValueField="code"/>
                        <FlexBlock CssStyle="margin-bottom:5px">
                            <DropDownEdit ID="updateFlagConditionForModify" SelectedIndex="0" Width="45" DataField="flagCondition"
                                          DataSource="${conditionDataSource}" DataTextField="name"
                                          DataValueField="code"/>
                        </FlexBlock>
                        <Label Text="旗帜:    "/>
                        <DropDownCheckBoxList ReportField="旗帜" ID="updateFlag" DropType='Image' CssClass="FlexAuto"
                                              DataField="sellerFlag" DataSource="${flagsSource}"
                                              DataValueField="code" DataTextField="name"
                                              Width="148" DropDownStyle="DropDownSearch" />
                    </FlowPanel>
                </NavGroup>
            </NavBar>
            <NavBar>
                <NavGroup Text="商品信息" Expanded="false" GroupName="GroupB"  ID="updatePtypeNav"   _quickIcon="bicon-biaojiqizhi">
                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                        <DropDownEdit ID="updatePtypeType" Width="105" DataField="ptypeType"
                                      SelectedIndex="0" DropDownStyle="DropDownList"
                                      OnChange="doChangeUpdatePtypeSelectType">
                            <ListItem Value="0" Text="按商品"/>
                            <ListItem Value="1" Text="按商品标签"/>
                            <ListItem Value="2" Text="按商品分类"/>
                        </DropDownEdit>
                        <SelectorEdit ReportVisible="false" ReportField="商品信息" ID="updatePtypeFullnameForModify"
                                      DataField="skuIds"
                                      Business="{'Name':'jxc.ptypeColumn', 'selectType':'Sku','showOnlyBaseunit':'false'}" ShowMDI='true'
                                      DisplayField="ptypeNames" OnEnterPress="openPtypeSelect"
                                      OnButtonClick="openPtypeSelect" CssClass="FlexAuto"
                                      OnSelectorSelected="selectPtype" AutoFocusNext="false"/>
                        <DropDownCheckBoxList ReportField="商品标签" ID="updatePtypeLabel" DataField="updatePtypeLabelIds"
                                              DropDownStyle="DropDownSearch"
                                              Width="148"
                                              DataSource="${ptypeLabels}"
                                              DataTextField="fullname" DataValueField="id"
                                              LayoutDirection="Vert"/>
                        <SelectorEdit ReportField="商品分类" ID="updatePtypeClass" DataField="updatePtypeClassIds"
                                      SelectorPage="/jxc/baseinfo/selector/ClassSelector.gspx"
                                      ShowMDI='true'
                                      DisplayField="classNames" OnEnterPress="openPtypeClassSelect"
                                      OnButtonClick="openPtypeClassSelect" CssClass="FlexAuto"
                                      OnSelectorSelected="selectUpdatePtypeClass" AutoFocusNext="false"
                        />
                    </FlowPanel>
                </NavGroup>
            </NavBar>
            <NavBar>
                <NavGroup Text="物流信息" Expanded="false" GroupName="GroupB"  ID="updateFreightNav"   _quickIcon="bicon-biaojiqizhi">
                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                        <Label Text="物流公司:"/>
                        <TextEdit ID="updateFreightName" CssClass="FlexAuto " ReportField="物流公司"
                                  NullDisplayText="" DataField="freightName" MaxLength="200"/>
                        <Label Text="物流单号:"/>
                        <TextEdit ID="updateFreightBillNo" CssClass="FlexAuto " ReportField="物流单号"
                                  NullDisplayText="" DataField="freightBillNo" MaxLength="200"/>
                    </FlowPanel>
                </NavGroup>
            </NavBar>
            <NavBar>
                <NavGroup Text="留言/备注" Align="Left" Expanded="false" GroupName="GroupB" _quickIcon="bicon-gonghai" ID="updateMemoNav">
                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                        <HPanel LayoutDirection='Horz'>
                            <Label Text="买家留言:" Width="60"/>
                            <DropDownEdit ID="updateBuyerMessage"  DataField="buyerMessage" ListItems='0=不过滤,1=无留言,2=有留言,3=不包含'
                                          Value='0' OnChange="setEditEnabled" Width="80" Tag="updateBuyerMessageMemo" DropDownStyle="DropDownList"/>
                        </HPanel>
                        <TextEdit ID="updateBuyerMessageMemo" Width="148" NullDisplayText="留言内容..."
                                  Enabled="false" MaxLength="200"
                                  DataField="buyerMessageMemo" ReportField="买家留言" CssClass="FlexAuto"/>
                        <VSpacer Height="5"/>
                        <HPanel LayoutDirection='Horz'>
                            <Label Text="卖家备注:" Width="60"/>
                            <DropDownEdit ID="updateSellerMemo"  DataField="sellerMemo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                          Value='0' OnChange="setEditEnabled" Width="80" Tag="updateSellerMemoMessage" DropDownStyle="DropDownList"/>
                        </HPanel>
                        <TextEdit ID="updateSellerMemoMessage" Width="148" NullDisplayText="备注内容..."
                                  Enabled="false" MaxLength="200"
                                  DataField="sellerMemoMessage" ReportField="卖家备注" CssClass="FlexAuto"/>
                        <VSpacer Height="5"/>
                        <HPanel LayoutDirection='Horz'>
                            <Label Text="系统备注:" Width="60"/>
                            <DropDownEdit ID="updateMemo"  DataField="memo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                          Value='0' OnChange="setEditEnabled" Width="80" Tag="updateMemoMessage" DropDownStyle="DropDownList"/>
                        </HPanel>
                        <TextEdit ID="updateMemoMessage" Width="148" NullDisplayText="备注内容..." Enabled="false"
                                  DataField="memoMessage" ReportField="系统备注" CssClass="FlexAuto" MaxLength="200"/>
                    </FlowPanel>
                </NavGroup>
            </NavBar>
<!--            <NavBar>-->
<!--                <NavGroup Text="质检信息" Expanded="false" GroupName="GroupB"  ID="updateQualityNav"   _quickIcon="bicon-biaojiqizhi">-->
<!--                    <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>-->
<!--                        <Label Text="质检机构:"/>-->
<!--                        <TextEdit ID="updatePlatformQualityOrgNameQuery" CssClass="FlexAuto " ReportField="质检机构"-->
<!--                                  NullDisplayText="" DataField="platformQualityOrgName" MaxLength="200"/>-->
<!--                    </FlowPanel>-->
<!--                </NavGroup>-->
<!--            </NavBar>-->
        </FlowPanel>
        <Block CssClass='BottomPanel margin0 pd10'>
            <HBlock CssClass='dflex mb5'>
                <Button Text="保存" CssClass='SpecialButton FlexAuto' OnClick="saveQuickQuery"/>
                <Button Text="取消" CssClass='FlexAuto' OnClick="cancelUpdateQuickQuery"/>
            </HBlock>
        </Block>
    </PopupBlock>
    <ReportAction ID="reportA" Tab="times,reportTab"/>

    <Style>
        .starLabel:first-letter {
        color: red;
        }

        .parent {

        width: 100%;
        height: 100%;
        }

        .parent .item1 {
        flex: 1;
        background-color: olive;

        overflow-y: scroll;
        }

        .parent .item2 {
        height: 100px;
        background-color: blueviolet;
        }

        .maingrid .mark {
        color: #fff;
        padding: 3px;
        font-size: 12px;
        display: inline-block;
        }

        .maingrid .GridBody .GridDynamicButtonBody a:hover {
        text-decoration: none;
        cursor: default;
        }

        .GridDynamicButtonBody ._horz {
        padding: 0 2px;
        }

        .ExpandFlexGrid .GridExpandCell {
        display: flex;
        }



        .TopPanel {
        border-bottom: 1px solid #e4e4f2;
        padding-left: 10px;
        position: relative;
        font-size: 13px;
        }

        .TreeNode
        .circle {
        margin-left: 3px;
        height: 14px;
        line-height: 14px;
        min-width: 14px;
        border-radius: 7px;
        font-size: 12px;
        background-color: #EA420F;
        color: #fff;
        text-align: center;
        align-self: center;
        padding: 0 4px;
        }

        .hideCheckBox.TreeNode .ChkBoxStyle{display:none;}

        .CaptionLeft .TabHeader .TabCaptions{flex-grow:0}
        .CaptionLeft .TabHeader .TabSpace,.CaptionLeft .TabHeader .TabContainer{flex-grow:1}
        .UI_ListItem.NoSelect{cursor:default;}
        .UI_ListItem.NoSelect:hover{border-color:#ddd;}

        .TableEdit.TableEditPlus .check-bangzhu{
        background-color: #fff;
        padding-left: 5px;
        border: 1px solid #e1e1e1;
        border-top: none;
        }

        .TableEdit.TableEditPlus .TextArea{
        border-bottom-color: transparent;
        }

        .TableEdit.TableEditPlus .TextArea:focus .TableEdit.TableEditPlus .TextArea:hover{
        border-bottom-color: #2288fc;
        }

        .BottomPanel2 {
        display: flex;
        margin: 0 !important;
        justify-content: space-between;
        flex-wrap: wrap;
        z-index: 3;
        overflow: unset;
        }
        .TableEdit .dflex .EditBlock {
        margin-right: 0px;
        }

        .htmlTipBlock{background-color:#fff;padding:7px 10px 10px 10px;color:#333;border-radius:4px;}
        .htmlTip{padding: 2px 10px 10px 10px;}
        .htmlTipBlock .p{display:block;line-height:30px;height:auto;}
        .htmlTipBlock .p:before{content:" ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}
        .htmlTipBlock .bg2{background-color:#FEFAF6;padding:10px;color:#fd8800;}

        .bottomQueryBlockItem {
        width: 49%;
        padding: 0 1px;
        margin-right: 0;
        }

        .bottomQueryBlockItem .RightIcon{
        float: right;
        }

        .fullWidth{
        width: 100%;
        }

        .myCssss {
        padding-bottom: 0 !important;
        }

        .myCssss .FlowItem:last-child {
        position: sticky; /* 智能浮动到底部 */
        bottom: 0; /* 距离底部为0 */
        background-color: #f2f4f7;
        z-index: 10; /* 确保按钮不被遮挡 */
        padding-bottom: 10px;
        }

        .jdHelpButton{
        overflow:unset;
        }

    </Style>
</Page>
