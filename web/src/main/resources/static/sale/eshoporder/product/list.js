Type.registerNamespace('sale.product');

sale.product.listAction = function () {
    sale.product.listAction.initializeBase(this);
};

sale.product.listAction.prototype = {
    _allowPropsRsp: false,
    context: function (cb) {
        var mode = this.queryString("mode");
        var response = $common.ajaxSync({
            url: "sale/eshoporder/product/getInitData/" + mode,
            type: 'get',
            router: 'ngp'
        });
        var productOperateLogTypes = [];
        var supportSyncToSonShopTypes = [];
        var shopTypeSourceItems = [];
        var supportModifyPlatformXcodeShopTypes = [];
        if (response.data && response.data.productOperateLogTypes) {
            productOperateLogTypes = response.data.productOperateLogTypes;
        }
        if (response.data && response.data.supportSyncToSonShopTypes) {
            supportSyncToSonShopTypes = response.data.supportSyncToSonShopTypes;
        }
        if (response.data && response.data.shopTypeSourceItems) {
            shopTypeSourceItems = response.data.shopTypeSourceItems;
        }
        if (response.data && response.data.supportModifyPlatformXcodeShopTypes) {
            supportModifyPlatformXcodeShopTypes = response.data.supportModifyPlatformXcodeShopTypes;
        }
        var rsp = $common.ajaxSync({
            url: "sale/eshoporder/product/supportStockSyncShopTypes",
            type: 'post',
            router: 'ngp'
        });
        var generalStockSyncShopTypes = [];
        var warehouseStockSyncShopTypes = [];
        if (rsp.data) {
            if (rsp.data.generalStockSyncShopTypes && rsp.data.generalStockSyncShopTypes.length > 0) {
                generalStockSyncShopTypes = rsp.data.generalStockSyncShopTypes;
            }

            if (rsp.data.warehouseStockSyncShopTypes && rsp.data.warehouseStockSyncShopTypes.length > 0) {
                warehouseStockSyncShopTypes = rsp.data.warehouseStockSyncShopTypes;
            }
        }


        var propsEnabled = $ms.ngpConfig.Sys.sysIndustryEnabledProps;
        var excelRelationEnabled = $ms.ngpConfig.Sys.sysFuncEshopPtypeManagerExcelRelationEnabled;
        var mainPicEnabled = $ms.ngpConfig.Sys.sysFuncEshopPtypeManagerMainPicEnabled;
        var showSmartCreateCombo = $ms.ngpConfig.Sys.sysFuncEshopPtypeManagerSmartCreateComboEnabled;
        var markEditEnable = $saleUtils.getPower('eshoporder.product.mark.edit');
        var xcodeEnable = $saleUtils.getPower('eshoporder.product.xcode.edit');
        var refreshEnabled = $saleUtils.getPower('eshoporder.product.refresh');
        var relationEnabled = $saleUtils.getPower('eshoporder.product.relation');
        var deleteEnabled = $saleUtils.getPower('eshoporder.product.delete');
        var createLocalEnabled = $saleUtils.getPower('eshoporder.product.createLocal');
        var downloadMainPic = $saleUtils.getPower('eshoporder.product.downloadMainPic');
        var stockSyncEnabled = $saleUtils.getPower('eshoporder.stock.sync');

        cb({
            propsEnabled: !!propsEnabled,
            mainPicEnabled: !!mainPicEnabled && downloadMainPic,
            showSmartCreateCombo: !!showSmartCreateCombo,
            showSyncToSonEshop: false,
            excelRelationEnabled: !!excelRelationEnabled,
            eshopList: response.data.eshopInfoList,
            selected: response.data.selectedEshopIds,
            shopTypeSourceItems: shopTypeSourceItems,
            configId: mode,
            title: "网店商品管理",
            editIcon: "aicon-bianji4",
            productOperateLogTypes: productOperateLogTypes,
            supportSyncToSonShopTypes: supportSyncToSonShopTypes,
            markEditEnable: markEditEnable,
            xcodeEnable: xcodeEnable,
            refreshEnabled: refreshEnabled,
            relationEnabled: relationEnabled,
            deleteEnabled: deleteEnabled,
            stockSyncEnabled: stockSyncEnabled,
            createLocalEnabled: createLocalEnabled,
            generalStockSyncShopTypes: generalStockSyncShopTypes,
            warehouseStockSyncShopTypes: warehouseStockSyncShopTypes,
            ktypeList: [],
            supportModifyPlatformXcodeShopTypes: supportModifyPlatformXcodeShopTypes
        });
    },

    initialize: function listAction$initialize() {
        sale.product.listAction.callBaseMethod(this, 'initialize');
        $common.addMouseEnterHandler(this.get_form().btnCreateCombo._rightBtn, $createDelegate(this, this.showdownloadConfigView));
        $common.addMouseLeaveHandler(this.get_form().btnCreateCombo._rightBtn, $createDelegate(this, this.hidedownloadConfigView));
        var selected = this.get_context("selected");
        var form = this.get_form();
        form.eshop.set_value(selected);
        this.doSkuQuery();
        this.showSyncProductRelation();
        this.reloadRulesAndKtypes();

    },
    reloadRulesAndKtypes: function () {
        var ktypeResponse = $common.ajaxSync({
            url: "sale/eshoporder/basic/ktypeList",
            type: 'post',
            router: 'ngp'
        });
        var ktypeList = ktypeResponse.data;
        this.set_context("ktypeList", ktypeList);
        var params = {};
        params.queryParams = {pageSize: 1000};
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->默认库存同步规则设置查询');
        var that = this;
        $common.ajax({
            url: '/sale/eshoporder/product/defaultRuleList',
            data: params,
            router: 'ngp',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                if (response.code == 200) {
                    var data = response.data;
                    if (data && data.total > 0) {
                        that._rebuildFormula(data.list);
                        that._doFillKtypeNames(data.list);
                        that.set_context("defaultRules", data.list);
                    }
                }
            }
        });
        this.loadCustomRules();
    },
    loadCustomRules: function () {
        var params = {};
        params.queryParams = {pageSize: 1000};
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->自定义库存同步规则查询');
        var response = $common.ajaxSync({
            url: '/sale/eshoporder/product/queryCustomRule',
            data: params,
            router: 'ngp'
        });
        $saleUtils._logEndTiming(startObj);
        var that = this;
        if (response.code == 200) {
            var data = response.data;
            if (data && data.total > 0) {
                that._rebuildFormula(data.list);
                that._doFillKtypeNames(data.list);
                that.set_context("customRules", data.list);
            }
        }
    },
    showdownloadConfigView: function (sender, e) {
        if (!e) e = sender; // hover的情况
        this.get_form().downloadConfigView.appendAt(e.target.parentElement);
    },
    hidedownloadConfigView: function (sender, e) {
        this.get_form().downloadConfigView.close();
    },
    doEshopSelectorSelected: function () {
        var form = this.get_form();
        var selected = form.eshop.get_value();
        this.set_context("selected", selected);
        this.showSyncProductRelation();
    },
    showSyncProductRelation: function () {
        var form = this.get_form();
        var relationEnabled = this.get_context("relationEnabled");
        if (!relationEnabled) {
            return;
        }
        var selectedEshopList = form.eshop.get_selectedItems();
        if (!selectedEshopList || selectedEshopList.length === 0) {
            form.btnSyncToSonEshop.set_visible(false);
            return;
        }
        var filterEshopList = this.filterSupportSyncToSnShop(selectedEshopList);
        form.btnSyncToSonEshop.set_visible(filterEshopList.length > 0);
    },
    filterSupportSyncToSnShop: function (eshopList) {
        var filterEshopList = [];
        var supportSyncToSonShopTypes = this.get_context("supportSyncToSonShopTypes");
        if (!supportSyncToSonShopTypes || supportSyncToSonShopTypes.length === 0) {
            return filterEshopList;
        }
        if (!eshopList || eshopList.length === 0) {
            return filterEshopList;
        }
        for (var i = 0; i < eshopList.length; i++) {
            var eshop = eshopList[i];
            if (supportSyncToSonShopTypes.includes(eshop.eshopType) && eshop.mainEshop) {
                filterEshopList.push(eshop);
            }
        }
        return filterEshopList;
    },

    doQueryTypeChange: function (sender) {
        var form = this.get_form();
        var tag = sender.get_value();
        if (tag == 1) {
            form.edQueryText.set_nullDisplayText("名称/简名/编号/商家编码");
        } else {
            form.edQueryText.set_nullDisplayText("名称/编号/商家编码/属性/SKU ID");
        }
    },
    syncRelationToSubEshop: function (sender) {
        var form = this.get_form();
        var selectedEshopList = form.eshop.get_selectedItems();
        var eshopList = this.get_context("eshopList");
        var filterSelectedEshopList = this.filterSupportSyncToSnShop(selectedEshopList);
        var selectedEshop = null;
        if (filterSelectedEshopList.length > 0) {
            selectedEshop = filterSelectedEshopList[0];
        }
        var filterEshopList = this.filterSupportSyncToSnShop(eshopList);
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/SyncPtypeRelationToSonShop.gspx", {
            selectedEshop: selectedEshop,
            eshopList: filterEshopList
        });
        modal.add_closed(function (pop) {
            if (form) {
                form.grid.get_pager().refresh();
            }
        });
    },

    doShowUnRelation: function () {
        var form = this.get_form();
        form.dropRelationState.set_value(2);
        var grid = form.grid;
        grid.get_pager().refresh();
    },

    doShowFilter: function () {
        var form = this.get_form();
        form.saveData();
        var allowed = form.grid.get_allowFilter();
        form.grid.set_allowFilter(!allowed);
    },

    doSkuQuery: function () {
        var form = this.get_form();
        var grid = form.grid;
        grid.get_pager().refresh({query: true});
    },

    _doBuildSkuQuery: function (form, params) {
        var selected = form.eshop.get_value();
        var queryType = form.edQueryType.get_value();
        this.set_context("selected", selected);
        var propsEnabled = this.get_context("propsEnabled");
        var queryParams = {
            otypeIdList: selected,
            platformStockState: form.dropStockState.get_value()
        };

        queryParams.mappingState = form.dropRelationState.get_value();
        queryParams.xcodeQueryType = form.dropXcode.get_value();
        queryParams.queryStockRuleEnabled = true;

        var queryText = form.edQueryText.get_value();
        if (queryType == 1) {
            queryParams.localInfo = queryText;
        } else if (queryType == 0) {
            queryParams.platformInfo = queryText;
        }
        var grid = form.grid;
        //列配置如果配置了展示查询的sql才进行查询
        this._doBuildSkuQueryByGridConfig(grid, queryParams);
        if (!propsEnabled) {
            queryParams.queryLocalPropEnabled = false;
        }
        this._buildQueryByFilter(params, queryParams);
        return queryParams;
    },

    _doBuildSkuQueryByGridConfig: function (grid, queryParams) {
        var configData = grid.get_configData();
        if (configData.ColumnsVisible) {
            if (configData.ColumnsVisible.localPicUrl) {
                queryParams.queryLocalPicEnabled = true;
            }
            if (configData.ColumnsVisible.brandName) {
                queryParams.queryLocalBrand = true;
            }
            if (!configData.ColumnsVisible.unitName) {
                queryParams.queryUnitNameEnabled = false;
            }
        }
    },

    _buildQueryByFilter: function (params, queryParams) {
        if (!params.queryParams || !params.queryParams.gridFilter) {
            return;
        }
        for (var i = 0; i < params.queryParams.gridFilter.length; i++) {
            var item = params.queryParams.gridFilter[i];
            var dataField = item.dataField;
            var dataValue = item.value;
            if (!dataValue || dataValue == '') {
                continue;
            }
            queryParams[dataField] = dataValue;
        }
    },

    doLogTabChange: function (sender) {
        var form = sender.get_form();
        var data = form.grid.get_selectedRowData();
        if (!data) {
            return;
        }
        var tab = form.downFlex.get_selectedTab();
        if (tab.get_idPart() == "tabSync") {
            form.stockLogGrid.get_pager().refresh();
        } else {
            form.logGrid.get_pager().refresh();
        }
    },

    doSelectionChanged: function (sender) {
        var data = sender.get_selectedRowData();
        if (!data) {
            return;
        }
        var form = sender.get_form();
        var tab = form.downFlex.get_selectedTab();
        if (tab.get_idPart() == "tabSync") {
            form.stockLogGrid.get_pager().refresh();
        } else {
            form.logGrid.get_pager().refresh();
        }
    },

    doDeleteSkus: function () {
        var form = this.get_form();
        var grid = form.grid;
        var selectedItems = grid.get_selectedItems();
        if (!selectedItems || selectedItems.length === 0) {
            $common.showError("请选择需要删除的SKU");
            return;
        }
        this.dealQtyBeforeInvoke(selectedItems);
        $common.confirm("是否批量删除所选SKU，选择确定后，继续删除SKU，取消后不删除。", function (r) {
            if (r) {
                var startObj = $saleUtils._getStartTiming('网店->网店商品管理->删除SKU')
                startObj.batchCount = selectedItems ? selectedItems.length : 1;
                $common.ajax({
                    url: "sale/eshoporder/product/deleteSkus",
                    data: selectedItems,
                    router: "ngp",
                    waiting: '正在刪除数据,请稍等',
                    success: function (response) {
                        $saleUtils._logEndTiming(startObj);
                        if (response.code != "200") {
                            return;
                        }
                        if (grid) {
                            grid.get_pager().refreshPage();
                        }
                    }
                });
            }
        }, this);
    },

    doBeginChange: function (sender, eventArgs) {
        var column = eventArgs.get_column()
        var data = sender.get_selectedRowData();
        var dataField = column.get_dataField();
        if (!data || !dataField) {
            eventArgs.set_cancel(true);
            return;
        }

        if (dataField == "qty") {
            var form = this.get_form();
            if (!data.ptypeId || 0 == data.ptypeId) {
                eventArgs.set_cancel(true);
            }
            if (data.warehouseStockSyncEnabled || data.eshopMultiStockSyncEnabled) {
                eventArgs.set_cancel(true);
                var modal = new Sys.UI.Form(form);
                var grid = form.grid;
                modal.add_ok(function (popForm) {
                    if (popForm.hfSkuData) {
                        var skuData = popForm.hfSkuData.get_value();
                        data = skuData;
                        grid.batchModifyRowData(null, [data]);
                    }
                });
                var param = {data: data};
                modal.showModal("sale/eshoporder/product/WarehouseAndMultiStockEdit.gspx", param);
            }
            return;
        }
        if (dataField == 'ruleName') {
            if (!data.ptypeId || 0 == data.ptypeId) {
                eventArgs.set_cancel(true);
                return;
            }
            var cell = sender.get_activeCell();
            this.doOpenSkuRuleSelector(sender, eventArgs, cell);
        }
    },

    doMouseHover: function (sender, eventArgs) {
        var form = this.get_form();
        form.ruleView.hide();
        form.ruleFlexPop.hide();
        var column = eventArgs.get_column();
        if (column == null) {
            return;
        }
        var dataField = column.get_dataField();
        var data = sender.get_mouseHoverRowData();

        form.warehouseStockPopup.hide();

        if (dataField == 'qty' || dataField == 'autoSyncEnabled' || dataField == 'ruleName') {
            if (data.disableTips && data.disableTips.length > 0) {
                $common.showToolTip(eventArgs.get_clientX() - 10, eventArgs.get_clientY(), data.disableTips);
                return;
            } else {
                $common.hideToolTip();
            }
        } else {
            $common.hideToolTip();
        }

        if (dataField == 'qty' && (data.warehouseStockSyncEnabled || data.eshopMultiStockSyncEnabled)) {
            var showPopup = false;
            if (!data.warehouseStocks && !data.multiTimeStocks) {
                this.loadWarehouseAndMultiStock(data);
            }
            form.warehouseGrid.set_visible(false);
            form.multiGrid.set_visible(false);
            if (data.warehouseStocks && data.warehouseStocks.length > 0) {
                showPopup = true;
                form.warehouseGrid.dataBind(data.warehouseStocks);
                form.warehouseGrid.set_visible(true);
            } else if (data.multiTimeStocks && data.multiTimeStocks.length > 0) {
                showPopup = true;
                form.multiGrid.dataBind(data.multiTimeStocks);
                form.multiGrid.set_visible(true);
            }

            if (showPopup) {
                form.warehouseStockPopup.popupAt(eventArgs.get_event().target);
            } else {
                form.warehouseStockPopup.hide();
            }
            return;
        }


        if (dataField != "ruleName") {
            return;
        }
        if (data) {
            var result = this.bindRuleViewData(data);
            if (result) {
                form.ruleView.popupAt(eventArgs.get_event().target);
            } else {
                form.ruleView.hide();
            }
        } else {
            form.ruleView.hide();
        }
    },
    loadWarehouseAndMultiStock: function (data) {
        var url = '/sale/eshoporder/product/queryMultiStockSync';
        this.dealQtyBeforeInvoke([data]);
        var response = $common.ajaxSync({
            url: url,
            data: [data],
            type: 'post',
            router: 'ngp'
        });
        this.dealQtyAfterInvoke([data]);
        if (response.code != 200 || !response.data) {
            return;
        }
        if (response.data && response.data.length > 0) {
            var stockData = response.data[0];
            if (data.warehouseStockSyncEnabled && stockData.wareHouseStocks && stockData.wareHouseStocks.length > 0) {
                data.warehouseStocks = [];
                for (var i = 0; i < stockData.wareHouseStocks.length; i++) {
                    var item = stockData.wareHouseStocks[i];
                    var warehouseStock = {
                        warehouseCode: item.warehouseCode,
                        warehouseName: item.warehouseName,
                        warehouseType: item.warehouseType,
                        syncQty: item.syncQty
                    };
                    data.warehouseStocks.push(warehouseStock);
                }
            }
            if (data.eshopMultiStockSyncEnabled && stockData.multiTimeStocks && stockData.multiTimeStocks.length > 0) {
                data.multiTimeStocks = [];
                for (var i = 0; i < stockData.multiTimeStocks.length; i++) {
                    var item = stockData.multiTimeStocks[i];
                    var multiTimeStock = {
                        timeType: item.timeType,
                        stockNum: item.stockNum,
                        timeDesc: item.timeDesc
                    }
                    data.multiTimeStocks.push(multiTimeStock);
                }
            }
        }
    },
    bindRuleViewData: function (data) {
        var form = this.get_form();
        var ruleId = data.syncRuleId;
        var rule = this.getRuleById(ruleId, data.otypeId);
        if (!rule) {
            return false;
        }
        this.filterRuleViewPopByRule(rule);
        return true;
    },
    getRuleById: function (ruleId, otypeId) {
        var form = this.get_form();
        if (!ruleId || ruleId == 0) {
            //默认规则
            var defaultRules = this.get_context("defaultRules");
            if (!defaultRules || defaultRules.length == 0) {
                return null;
            }

            if (!otypeId) {
                return null;
            }
            for (var i = 0; i < defaultRules.length; i++) {
                if (defaultRules[i].otypeId == otypeId) {
                    return defaultRules[i];
                }
            }
        } else {
            //自定义规则
            var customRules = this.get_context("customRules");
            if (!customRules || customRules.length == 0) {
                return null;
            }
            for (var i = 0; i < customRules.length; i++) {
                if (customRules[i].id == ruleId) {
                    return customRules[i];
                }
            }
            //如果是加载后新增的规则,重新加载缓存
            this.loadCustomRules();
            for (var i = 0; i < customRules.length; i++) {
                if (customRules[i].id == ruleId) {
                    return customRules[i];
                }
            }
        }
        return null;
    },
    filterRuleViewPopByRule: function (rule) {
        if (!rule) {
            return;
        }
        var form = this.get_form();
        var openStr = '<font color="#3BA33D">开启</font>';
        var closeStr = '关闭';
        form.lblStock.set_text('同步仓库: ' + rule.ktypeNames);
        form.lblFormula.set_text('<span>同步公式: <font color="#F17800">' + rule.formula + '</font></span>');
        if (rule.autoSyncEnabled) {
            form.lblSyncStock.set_text('<span>库存自动同步: ' + openStr + '</span>');
        } else {
            form.lblSyncStock.set_text('<span>库存自动同步: ' + closeStr + '</span>');
        }
        if (rule.zeroQtySyncEnabled) {
            form.lblZeroSync.set_text('<span>0库存同步: ' + openStr + '</span>');
        } else {
            form.lblZeroSync.set_text('<span>0库存同步: ' + closeStr + '</span>');
        }
        if (rule.warehouseStockSyncEnabled) {
            form.lblWarhouseStockSync.set_text('<span>分仓库存同步: ' + openStr + '</span>');
        } else {
            form.lblWarhouseStockSync.set_text('<span>分仓库存同步: ' + closeStr + '</span>');
        }

        if (rule.eshopMultiStockSyncEnabled) {
            form.lblMultiStockSync.set_text('<span>时效库存同步: ' + openStr + '</span>');
            if (!rule.multiStockSyncDetailList || rule.multiStockSyncDetailList.length == 0) {
                rule.multiStockSyncDetailList = this.loadMultiStockSyncDetails(rule.id);
                if (rule.multiStockSyncDetailList && rule.multiStockSyncDetailList.length > 0) {
                    for (var i = 0; i < rule.multiStockSyncDetailList.length; i++) {
                        var detail = rule.multiStockSyncDetailList[i];
                        detail.formula = this.buildFormulaFromCalculate(detail.calculate);
                    }
                }
            }
            if (rule.multiStockSyncDetailList && rule.multiStockSyncDetailList.length > 0) {
                form.multiStockGrid.dataBind(rule.multiStockSyncDetailList);
                form.multiStockGrid.set_visible(true);
            }
        } else {
            form.multiStockGrid.set_visible(false);
            form.multiStockGrid.dataBind(null);
            form.lblMultiStockSync.set_text('<span>时效库存同步: ' + closeStr + '</span>');
        }
    },
    loadMultiStockSyncDetails: function (ruleId) {
        var params = {};
        params.ruleId = ruleId;
        var url = '/sale/eshoporder/product/queryMultiDetails';
        var response = $common.ajaxSync({
            url: url,
            data: params,
            type: 'post',
            router: 'ngp'
        });
        if (response.code != 200 || !response.data) {
            return;
        }
        return response.data;
    },
    doBindPagerDataSource: function (path, params, binData) {
        var form = this.get_form();
        var topic = this._buildQueryLogTopic(params);
        params.queryParams = this._doBuildSkuQuery(form, params);
        this.set_context("queryParams", params);
        if (path === "GetRealPagerSize") {
            $common.ajax({
                url: '/sale/eshoporder/product/skuListCount',
                data: params,
                router: 'ngp',
                success: function (sizeResponse) {
                    binData(sizeResponse.data);
                }
            });
            return;
        }
        var that = this;
        var startObj = $saleUtils._getStartTiming(topic);
        $common.ajax({
            url: '/sale/eshoporder/product/skuList',
            data: params,
            router: 'ngp',
            waiting: '正在查询数据,请稍等',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                if (response.code != 200) {
                    $common.alert(response.message);
                    binData({itemList: [], itemCount: 0});
                    return;
                }
                var data = response.data;
                if (!data || data.total == 0) {
                    binData({itemList: [], itemCount: 0});
                } else {
                    that._buildOperationBtn(data.list);
                    binData({itemList: data.list, itemCount: data.total});
                    that._doFilterOtherPageData(form, data.list);
                    that._bindUnRelationCount(form);
                }
            }
        });
    },

    _buildQueryLogTopic: function (params) {
        var topic = '网店->网店商品管理';

        if (params.queryParams.query) {
            topic += "->查询"
        } else {
            topic += "->筛选"
        }
        return topic;
    },

    _buildOperationBtn: function (list) {
        for (var i = 0; i < list.length; i++) {
            var pageData = list[i];
            this._doBuildOperationBtn(pageData);
            pageData.index = i;
        }
    },

    _doBuildOperationBtn: function (pageData) {
        var relation = true;
        var btns = [];
        if (!pageData.ptypeId || pageData.ptypeId == 0) {
            relation = false;
        }
        var mappingType = pageData.mappingType;
        if (!relation) {
            btns.push("0=缺失 btn_queshi tishikong");
        } else {
            if (!mappingType || mappingType == 0) {
                btns.push("1=手工 btn_zidong tishikong");
            } else {
                btns.push("2=自动 btn_zidong tishikong");
            }
        }
        btns.push("3=绑定 btn_bangding");
        pageData.operation = btns.join(",");
    },

    //异步加载数据： 标记、库存、未对应的数量
    _doFilterOtherPageData: function (form, list) {
        if (!list || list.length == 0) {
            return;
        }
        this.dealQtyBeforeInvoke(list);
        var grid = form.grid;
        list = this._doFilterMark(grid, list, true);
        var url = "/sale/eshoporder/product/queryStock";
        var response = $common.ajaxSync({
            url: url,
            data: list,
            router: 'ngp'
        });
        if (response.code == 200) {
            list = response.data;
        }
        var supportSyncShoptypes = this.get_context('generalStockSyncShopTypes')
        var stockSyncEnabled = this.get_context('stockSyncEnabled');
        for (var i = 0; i < list.length; i++) {
            var data = list[i];
            var rowIndex = data.index;
            var rowData = grid.getRowData(rowIndex, true);

            if (!rowData.ptypeId
                || rowData.ptypeId == '0'
                || !stockSyncEnabled
                || !this.checkIsSupportSyncShoptype(supportSyncShoptypes, data.eshopType)) {
                data.qty = '';
            } else if (rowData.qtyHasModified) {
                data.qty = rowData.qty;
            }
            data.qtyHasModified = true;
        }
        grid.batchModifyRowData(null, list);
    },

    _doFilterMark: function (grid, list, skip) {
        if (!skip) {
            this.dealQtyBeforeInvoke(list);
        }
        var url = "/sale/eshoporder/product/queryMark";
        var response = $common.ajaxSync({
            url: url,
            data: list,
            router: 'ngp'
        });
        if (response.code == 200) {
            list = response.data;
        }
        if (!skip) {
            this.dealQtyAfterInvoke(list);
        }
        return list;
    },

    _bindUnRelationCount: function (form) {
        var selected = this.get_context("selected");
        if (!selected) {
            return;
        }
        this._rememberLastSelected();
        var url = '/sale/eshoporder/product/skuUnRelationCount';
        var params = {
            otypeIdList: selected,
            platformStockState: form.dropStockState.get_value(),
            mappingState: 2
        };
        $common.ajax({
            url: url,
            data: params,
            router: 'ngp',
            success: function (response) {
                var count = response.data;
                if (count == 0 || count == "0") {
                    form.lbUnRelationCount.set_text(0);
                    form.lbUnRelationCount.set_visible(false);
                } else {
                    form.lbUnRelationCount.set_visible(true);
                    form.lbUnRelationCount.set_text(count);
                }
            }
        });
    },

    _rememberLastSelected: function () {
        var selected = this.get_context("selected");
        var url = "sale/eshoporder/common/setUserData"
        var dataList = [];
        var subName = this.get_context("configId") == "ptype" ? "pl_product_page_data" : "pl_product_page_data_stock";
        if (selected != null && selected.length > 0) {
            dataList.push({
                subName: subName,
                subValue: selected.join(','),
                description: "网店商品对应：上一次选择的网店"
            })
        }
        $common.ajax({
            url: url,
            data: dataList,
            type: 'post',
            router: 'ngp'
        });
    },

    doSkuRefresh: function () {
        var form = this.get_form();
        var grid = form.grid;
        var selectedEshopList = form.eshop.get_selectedItems();
        var eshopList = this.get_context("eshopList");
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/RefreshToErp.gspx", {
            selectedEshopList: selectedEshopList,
            eshopList: eshopList
        });
        modal.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            grid.get_pager().refresh();
        });
    },

    doSingleSkuRefresh: function () {
        var form = this.get_form();
        var grid = form.grid;
        var item = grid.get_selectedRowData();
        if (!item) {
            return;
        }
        var reqParams = {};
        reqParams.eshopId = item.otypeId;
        reqParams.numIds = [item.platformNumId];
        var _this = this;
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->刷新网店商品');
        $common.ajax({
            url: '/sale/eshoporder/product/doSingleRefresh',
            data: reqParams,
            router: 'ngp',
            waiting: '正在刷新网店商品,请稍等',
            success: function (result) {
                $saleUtils._logEndTiming(startObj);
                if (result.code != 200) {
                    $common.showError('错误：' + result.data, {timer: 2500});
                    return;
                }
                _this._doSelectedItemsRefresh(grid, [item]);
                $common.showOk('刷新成功', {timer: 2000});
            }
        });
    },

    _doSelectedItemsRefresh: function (grid, items) {
        var propsEnabled = this.get_context("propsEnabled");
        this.dealQtyBeforeInvoke(items);
        var queryParams = {
            dataList: items
        };
        this._doBuildSkuQueryByGridConfig(grid, queryParams);
        if (!propsEnabled) {
            queryParams.queryLocalPropEnabled = false;
        }
        queryParams.queryStockRuleEnabled = true;
        var that = this;
        $common.ajax({
            url: "sale/eshoporder/product/dataItemsRefresh",
            data: queryParams,
            type: 'post',
            router: 'ngp',
            success: function (result) {
                if (result.code != 200) {
                    $common.alert(result.message);
                } else {
                    that.dealQtyAfterInvoke(result.data);
                    for (var i = 0; i < result.data.length; i++) {
                        var rowData = result.data[i];
                        that._doBuildOperationBtn(rowData);
                        grid.modifyRowData(rowData.index, rowData);
                    }
                }
            }
        });
    },

    doSyncQtyChange: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        rowData.qtyHasModified = true;
        grid.modifyRowData(rowData.index, rowData);
    },

    doPTypeNameChange: function (sender, args) {
        var rowData = sender.get_grid().get_selectedRowData();
        if (!rowData || !rowData.platformNumId) {
            return;
        }
        var text = sender.get_value();
        if (text && text != '') {
            return;
        }
        var oldVal = args.get_oldValue();
        if (rowData.mappingType == 1) {
            sender.set_value(oldVal);
        }
        var that = this;
        $common.confirm("检测到您清空了商品名称，是否需要清空对应关系呢？", function (result) {
            if (result) {
                that._executeClearRelation([rowData]);
            } else {
                sender.set_value(oldVal);
            }
        })
    },
    doPtypeColumnButtonClick: function (sender) {
        sender.set_selectorPageParams({
            selectType: "Sku",
            multiSelect: true,
            comboMultiSelect: false,
            vchtype: "sale",
            inputQty: false,
            comboInputQty: false,
            StepButton: true,
            inputPrice: true,
            existedSku: true,
            showStockQty: false,
            showStockAllQty: false,
            showStockQtyCheck: false,
            showInventoryQty: false,
            showComboXcodes: true,
            showComboSku: true,
            showHistoryQuery: true,
            inputGift: true
        });
    },

    doPtypeNameSelected: function (sender) {
        var selectedData = sender.get_selectedRowData();
        var form = this.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData()
        this._buildBindRelationData([rowData], [selectedData]);
    },

    doPlatformXcodeChange: function (sender, args) {
        var xcode = sender.get_value();
        var grid = sender.get_grid();
        var rowData = grid.get_selectedRowData();
        var oldXcode = args.get_oldValue();
        rowData.oldPlatXCode = oldXcode;
        var that = this;
        if (!xcode || xcode == '') {
            $common.confirm("是否确认要清空网店商品商家编码，并同步到平台？", function (result) {
                if (result) {
                    that._doModifyPlatformXcodeByRowData(grid, rowData, oldXcode, that);
                } else {
                    rowData.platformXcode = oldXcode;
                    grid.modifyRowData(rowData.index, rowData);
                }
            })
        } else {
            this._doModifyPlatformXcodeByRowData(grid, rowData, oldXcode, that);
        }
    },

    _doModifyPlatformXcodeByRowData: function (grid, rowData, oldXcode, that) {
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->修改网店商品商家编码');
        this.dealQtyBeforeInvoke([rowData]);
        var _this = this;
        $common.ajax({
            url: "sale/eshoporder/product/modifyPlatformXcode",
            data: rowData,
            type: 'post',
            router: 'ngp',
            waiting: '正在执行同步到平台,请稍等',
            success: function (changeResult) {
                $saleUtils._getStartTiming(startObj);
                _this.dealQtyAfterInvoke([rowData]);
                if (changeResult.code != 200) {
                    rowData.platformXcode = oldXcode;
                    $common.alert(changeResult.message);
                    grid.modifyRowData(rowData.index, rowData);
                } else {
                    that._doSelectedItemsRefresh(grid, [rowData]);
                }
            }
        });
    },

    doOperation: function (sender, args) {
        var form = sender.get_form();
        var grid = form.grid;
        var tag = args.get_buttonValue();
        if (!tag || tag != 3) {
            return;
        }
        var rowData = grid.get_selectedRowData();
        var items = [];
        items.push(rowData);
        this._doSelectBindData(sender, items, this._buildBindRelationData.bind(this))
    },

    doBatchBindLocal: function (sender) {
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length === 0) {
            $common.showError("请选择需要手工绑定的商品");
            return;
        }
        this._doSelectBindData(sender, items, this._buildBindRelationData.bind(this))
    },

    doBatchClearRelation: function () {
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length === 0) {
            $common.showError("请选择需要解除手工绑定的商品");
            return;
        }
        this._executeClearRelation(items);
    },

    _executeClearRelation: function (items) {
        //只处理手工对应的商品
        var existXcodeMapping = false;
        var manalMappingItems = [];
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            //未对应
            if (!item.ptypeId || item.ptypeId == 0) {
                continue;
            }
            if (!item.mappingType || item.mappingType == 0) {
                manalMappingItems.push(item);
            } else {
                existXcodeMapping = true;
            }
        }
        if (existXcodeMapping) {
            var msg = '已过滤按商家编码自动对应的网店商品SKU';
            if (manalMappingItems.length === 0) {
                msg += "，过滤后没有需要解除手工绑定的商品";
            }
            $common.showInfo(msg, {
                only: true,
                timer: 4000
            });
            if (manalMappingItems.length === 0) {
                return;
            }
        }
        if (manalMappingItems.length === 0) {
            $common.showInfo('没有需要解除手工绑定的商品', {
                only: true,
                timer: 4000
            });
            return;
        }
        var form = this.get_form();
        var grid = form.grid;
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->解除手工绑定');
        startObj.batchCount = manalMappingItems.length;
        var _this = this;
        this.dealQtyBeforeInvoke(manalMappingItems);

        $common.ajax({
            url: "sale/eshoporder/product/batchUnbindManualRelation",
            data: manalMappingItems,
            waiting: '正在执行....',
            type: 'post',
            router: 'ngp',
            success: function (changeResult) {
                $saleUtils._logEndTiming(startObj);
                _this.dealQtyAfterInvoke(manalMappingItems);
                if (changeResult.data != "") {
                    $common.showInfo(changeResult.data);

                }
                _this._doSelectedItemsRefresh(grid, manalMappingItems);
            }
        });
    },

    doClearRelation: function () {
        var form = this.get_form();
        var grid = form.grid;
        var item = grid.get_selectedRowData();
        if (!item) {
            return;
        }
        var items = [];
        items.push(item);
        this._executeClearRelation(items);
    },

    doBindLocal: function (sender) {
        var form = this.get_form();
        var grid = form.grid;
        var item = grid.get_selectedRowData();
        if (!item) {
            return;
        }
        var items = [];
        items.push(item);
        this._doSelectBindData(sender, items, this._buildBindRelationData.bind(this))
    },

    _doSelectBindData: function (sender, rowDataList, bindFunc) {
        var popForm = new Sys.UI.Form(sender);
        var params = {
            selectType: "Sku",
            multiSelect: true,
            comboMultiSelect: false,
            vchtype: "sale",
            inputQty: true,
            comboInputQty: false,
            StepButton: true,
            inputPrice: true,
            existedSku: true,
            showStockQty: false,
            showStockAllQty: false,
            showStockQtyCheck: false,
            showInventoryQty: false,
            showComboXcodes: true,
            showComboSku: true,
            tagsDataSelectedConfirmArray: ["已选多个sku，将自动创建为套餐与网店商品进行对应，请确认是否继续"],
            showHistoryQuery: true,
            inputGift: true
        }
        popForm.showModal("jxc/recordsheet/selector/PtypeSelector.gspx", params);
        popForm.add_closed(function (args) {
            var dataList = args.get_form().dataList;
            if (dataList && dataList.length > 0) {
                bindFunc(rowDataList, dataList);
            }
        });
    },

    _buildBindRelationData: function (items, dataList) {
        var form = this.get_form();
        var grid = form.grid;
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->手工绑定系统商品');
        startObj.batchCount = items.length;
        //$debug.traceDump(dataList);
        if (dataList.length > 1 || dataList[0].unitQty > 1) {
            //选择了多个sku则快速创建为套餐
            $eshoppower.checkPowerFalse("baseinfo.combo.add");
            this._buildComboWhenBindMoreSku(grid, items, dataList);
        } else {
            var data = dataList[0];
            this._doBindRelationData(grid, data, items);
        }
        this.dealQtyBeforeInvoke(items)
        var url = "sale/eshoporder/product/doBindRelation";
        var response = $common.ajaxSync({
            url: url,
            data: items,
            router: 'ngp'
        });
        this._doFilterOtherPageData(form, items);
        this.dealQtyAfterInvoke(items);
        startObj.batchCount = items.length;
        $saleUtils._logEndTiming(startObj);
        if (!response) {
            //如果绑定失败了在重新查询一次
            this._doSelectedItemsRefresh(grid, items);
        } else {
            this._doSingleBindModifyXcode(items);
            var relationState = form.dropRelationState.get_value();
            if (relationState == 2) {
                var _this = this;
                window.setTimeout(function () {
                    _this.doSkuQuery();
                }, 500);
            } else {
                var old = form.lbUnRelationCount.get_text();
                var newVal = old - items.length;
                form.lbUnRelationCount.set_text(newVal);
            }
        }
    },

    _doSingleBindModifyXcode: function (items) {
        if (items.length > 1) {
            return;
        }
        var item = items[0];
        var msg = "";
        var modifyLocalXcode = false;
        var supportModifyPlatformXcodeShopTypes = this.get_context("supportModifyPlatformXcodeShopTypes");
        if (supportModifyPlatformXcodeShopTypes && supportModifyPlatformXcodeShopTypes.includes(item.eshopType)) {
            if (item.platformXcode == "" && item.xcode != "") {
                msg = "系统商品存在商家编码，是否将系统商品的商家编码修改写入网店商品？";
            }
        }
        if (item.platformXcode != "" && item.xcode == "") {
            msg = "网店商品存在商家编码，是否将网店商品的商家编码修改写入系统商品？";
            modifyLocalXcode = true;
        }
        if (msg !== "") {
            var _this = this;
            $common.alert(msg, {
                title: '提示',
                iconName: '',
                buttons: ['修改商家编码', '关闭'],
                btnClass: ['SpecialButton', ''],
                handler: function (result) {
                    if (result === 0) {
                        _this._modifyXcodeByRowData(item, modifyLocalXcode);
                    }
                },
                className: 'AlertSelf'
            });
        }
    },
    _modifyXcodeByRowData: function (item, modifyLocalXcode) {
        var form = this.get_form();
        if (modifyLocalXcode) {
            var oldPtypeXcode = item.xcode;
            item.xcode = item.platformXcode;
            this._doModifyLocalXcodeByRowData(form.grid, item, oldPtypeXcode, this);
        } else {
            var oldPlatformXcode = item.platformXcode;
            item.oldPlatXCode = oldPlatformXcode;
            item.platformXcode = item.xcode;
            this._doModifyPlatformXcodeByRowData(form.grid, item, oldPlatformXcode, this);
        }
    },
    _doModifyLocalXcodeByRowData: function (grid, rowData, oldPtypeXcode, that) {
        this.dealQtyBeforeInvoke([rowData]);
        $common.ajax({
            url: "sale/eshoporder/product/modifyLocalXcode",
            data: rowData,
            type: 'post',
            router: 'ngp',
            waiting: '正在执行同步到平台,请稍等',
            success: function (changeResult) {
                if (changeResult.code != 200) {
                    rowData.xcode = oldPtypeXcode;
                    grid.modifyRowData(rowData.index, rowData);
                    $common.alert(changeResult.message);
                } else {
                    that._doSelectedItemsRefresh(grid, [rowData]);
                }
            }
        });
    },
    _doBindRelationData: function (grid, data, selectItems) {
        if (!data.unitId) {
            data.unitId = data.unit.id;
        }
        for (var i = 0; i < selectItems.length; i++) {
            var item = selectItems[i];
            item.ptypeId = data.id;
            item.ptypeCode = data.usercode;
            item.skuId = data.sku.id;
            item.unitId = data.unitId;
            item.unitName = data.unit.unitName;
            item.ptypeName = data.fullname;
            item.xcode = data.xcode;
            item.mappingType = 0;
            item.propNames = data.propFormat;
            item.propValueNames = data.propValues;
            item.costPrice = data.costPrice;
            item.ptypeType = data.ptypeType;
            item.brandName = data.brandName;
            item.standard = data.standard;
            item.localPicUrl = data.picUrl;
            item.pcategory = data.pcategory;
            item.operation = "1=手工 btn_zidong tishikong, 3=绑定 btn_bangding";
        }
        grid.batchModifyRowData(null, selectItems);
    },

    _buildComboWhenBindMoreSku: function (grid, items, dataList) {
        var xcode = items[0].platformXcode;
        var fullName = items[0].platformFullname;
        var ptypeCode = items[0].pmXcode;
        var prop = items[0].platformPropertiesName;
        var picUrl = items[0].platformPicUrl;
        var createComboParams = {
            comboName: (prop + fullName).substring(0, 200),
            comboCode: ptypeCode.substring(0, 200),
            xcode: xcode.substring(0, 200),
            picUrl: picUrl
        }
        var comboDetail = [];
        for (var i = 0; i < dataList.length; i++) {
            var data = dataList[i];
            var detail = {};
            detail.ptypeId = data.id;
            detail.ptypeCode = data.usercode;
            detail.ptypeName = data.fullname;
            detail.xcode = data.xcode;
            detail.skuId = data.sku.id;
            detail.unitId = data.unitId;
            detail.unitName = data.unit.unitName;
            detail.unitCode = data.unit.unitCode;
            detail.qty = data.unitQty;
            detail.retailPrice = data.price;
            detail.gifted = data.gifted;
            detail.pcategory = data.pcategory;
            comboDetail.push(detail);
        }
        createComboParams.details = comboDetail;
        var url = "sale/eshoporder/product/quickCreateCombo";
        var response = $common.ajaxSync({
            url: url,
            data: createComboParams,
            type: 'post',
            router: 'ngp'
        });
        if (response.code != '200') {
            $common.showError(response.message);
            return;
        }
        if (response.data) {
            if (response.data.code == "201") {
                var localCombo = response.data.data;
                if (localCombo) {
                    var that = this;
                    $common.confirm("已有相同明细的套餐存在，无法新增套餐！是否使用已有套餐对应", function (result) {
                        if (result) {
                            that._buildComboData(localCombo);
                            that._doBindRelationData(grid, localCombo, items);
                        }
                    })
                }
            } else if (response.data.code == "200") {
                var combo = response.data.data;
                if (combo) {
                    this._buildComboData(combo);
                    this._doBindRelationData(grid, combo, items);
                }
            } else {
                $common.showError(response.data.message);
            }
        }
    },

    //套餐返回的数据跟SKU返回的格式有点差异，需要调整一下
    _buildComboData: function (data) {
        data.pcategory = 2;
        if (!data.sku) {
            if (data.skus && data.skus.length() > 0) {
                data.sku = data.skus[0];
            }
        }
        if (!data.unit) {
            if (data.units && data.units.length > 0) {
                data.unit = data.units[0];
            }
        }
        if (!data.xcode) {
            if (data.xcodes && data.xcodes.length > 0) {
                data.xcode = data.xcodes[0].xcode;
            }
        }
        if (!data.picUrl) {
            if (data.pics && data.pics.length > 0) {
                data.picUrl = data.pics[0].picUrl;
            }
        }
    },

    doBatchMark: function () {
        var form = this.get_form();
        var modal = new Sys.UI.Form(form);
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length == 0) {
            $common.showError('请选择需要打标的网店商品');
            return;
        }
        var param = {
            rowDataList: items
        };
        var _this = this;
        modal.add_ok(function () {
            var list = _this._doFilterMark(grid, items);
            if (list && list.length > 0) {
                grid.batchModifyRowData(null, list);
            }
        });
        modal.showModal("sale/eshoporder/product/mark.gspx", param);
    },
    doSingleMark: function (sender) {
        var form = this.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        var param = {
            rowDataList: [rowData]
        };
        var _this = this;
        var modal = new Sys.UI.Form(form);
        modal.add_ok(function () {
            var list = _this._doFilterMark(grid, param.rowDataList);
            if (list && list.length > 0) {
                grid.batchModifyRowData(null, list);
            }
        });
        modal.showModal("sale/eshoporder/product/mark.gspx", param);
    },
    doCreatePtype: function () {
        $eshoppower.checkPowerFalse("baseinfo.ptype.add");
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        this._executeCreatePtype([], items, false);
    },
    doCreatePropPtype: function () {
        $eshoppower.checkPowerFalse("baseinfo.ptype.add");
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length === 0) {
            $common.showError("请先选择商品");
            return;
        }
        var attrRelations = this._doGetAttrRelation(form, items);
        if (!attrRelations) {
            return;
        }
        this._executeCreatePtype(attrRelations, items, true);
    },
    _executeCreatePtype: function (attrRelations, items, createPropPtype) {
        var form = this.get_form();
        var grid = form.grid;
        var createPtypeParams = new Object();
        createPtypeParams.attrRelations = attrRelations;
        createPtypeParams.skus = items;
        createPtypeParams.createPropPtype = createPropPtype;
        var parameter = {
            process: "createPropPtype",
            tittle: "生成系统属性商品",
            tipsType: "createPtype",
            createPtypeParams: createPtypeParams,
            totalSku: items.length
        };
        var modal = new Sys.UI.Form(form);
        var _this = this;
        modal.showModal("sale/eshoporder/product/process.gspx", parameter);
        modal.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            form.success = true;
            _this._doSelectedItemsRefresh(grid, items);
        });
    },
    _doGetAttrRelation: function (form, items) {
        this.dealQtyBeforeInvoke(items);
        var attrResponse = $common.ajaxSync({
            url: "sale/eshoporder/product/attrRelationList",
            data: items,
            router: 'ngp'
        });
        this.dealQtyAfterInvoke(items);
        if (attrResponse.code != 200) {
            $common.alert(attrResponse.message);
            return [];
        }
        var attrList = attrResponse.data;
        if (!attrList || attrList.length == 0) {
            return attrList;
        }
        var relationList = [];
        var unRelationList = [];
        for (var i = 0; i < attrList.length; i++) {
            var attr = attrList[i];
            if (attr.propId && attr.propId > 0) {
                relationList.push(attr);
                continue;
            }
            unRelationList.push(attr);
        }
        if (unRelationList.length === 0) {
            return attrList;
        }
        var parameter = {
            unRelationProps: unRelationList
        };
        var modal = new Sys.UI.Form(form);
        var _this = this;
        modal.showModal("sale/eshoporder/product/AttrRelationForm.gspx", parameter);
        modal.add_ok(function (frm) {
            var attrs = frm.data;
            if (attrs && attrs.length > 0) {
                for (var i = 0; i < attrs.length; i++) {
                    relationList.push(attrs[i]);
                }
            }
            _this._executeCreatePtype(relationList, items);
        })
    },

    //以下库存同步相关方法
    doSyncRuleConfig: function () {
        var form = this.get_form();
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/StockRuleList.gspx", {
            mode: 'edit',
            title: '自定义库存同步规则',
            ktypeList: this.get_context("ktypeList")
        });
    },

    modifyDefaultRule: function () {
        var form = this.get_form();
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/StockDefaultRuleList.gspx", {ktypeList: this.get_context("ktypeList")});
    },

    doSkuStockSync: function () {
        var form = this.get_form();
        var items = form.grid.get_selectedItems();
        if (!items || items.length === 0) {
            $common.showWarn("请勾选需要同步库存的商品");
            return;
        }
        var needUpdateRows = [];
        var skipCount = this.filterUnMappingItems(items, needUpdateRows);
        if(needUpdateRows.length === 0){
            $common.showWarn("请先绑定对应关系后再操作库存同步");
            return;
        }

        if(skipCount > 0){
            $common.showWarn("有" + skipCount + "个商品sku未绑定对应关系，不会进行库存同步");
        }

        var modal = new Sys.UI.Form(form);
        var parameter = {
            tipsType: "stockSync",
            tittle: "库存同步",
            data: needUpdateRows
        };
        modal.showModal("sale/eshoporder/product/process.gspx", parameter);
    },


    doOpenSkuRuleSelector: function (sender, args, cell) {
        var form = this.get_form();
        var popup = this.get_form().ruleFlexPop;
        var data = form.grid.get_selectedRowData();
        if (!data.ptypeId || 0 == data.ptypeId) {
            args.set_cancel(true);
            return;
        }
        if (cell) {
            var column = args.get_column();
            column.set_hasPopup(true);
            popup.popupAt(cell, {minHeight: 500});
        } else {
            var column = sender.get_column();
            column.set_hasPopup(true);
            popup.popupAt(sender, {minHeight: 500});
        }
    },

    doCreateAndUploadXcode: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length <= 0) {
            $common.showWarn('请选择需要生成网店编码的网店商品');
            return;
        }
        var modal = new Sys.UI.Form(form);
        var parameter = {
            data: items,
            nodeData: null
        };
        var _this;
        modal.add_ok(function () {
            grid.get_pager().refresh();
        });
        modal.showModal("sale/eshoporder/product/GenerateXcodeRule.gspx", parameter);
    },
    batchOpenSyncState: function (sender) {
        this.batchModifySyncState(sender, true);
    },
    batchCloseSyncState: function (sender) {
        this.batchModifySyncState(sender, false);
    },
    doCloseSyncStock: function (sender) {
        this.doModifySyncStock(sender, false);
    },
    doOpenSyncStock: function (sender) {
        this.doModifySyncStock(sender, true);
    },
    doModifySyncStock: function (sender, isOpen) {
        var form = this.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        if (!rowData) {
            $common.showWarn('请选择需要' + (isOpen ? '开启' : '关闭') + '库存自动同步的网店商品');
            return;
        }
        var items = [rowData];
        this.batchModifyStockSyncItems(form, grid, items, isOpen);
    },
    batchModifyStockSyncItems: function (form, grid, items, isOpen) {
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->' + (isOpen ? '开启' : '关闭') + '库存自动同步');
        this._updateItemsSelectedValue(items);
        var needUpdateRows = [];
        var skipCount = this.filterUnMappingItems(items, needUpdateRows);

        if (needUpdateRows.length <= 0) {
            $saleUtils._logEndTiming(startObj);
            $common.showWarn('请先完成商品对应再操作库存自动同步开关');
            return;
        }
        var message = '';
        if (skipCount > 0) {
            message = '因商品未对应,过滤sku' + skipCount + '条; ';
        }
        var params = {};
        params.isOpen = isOpen ? '1' : '0';
        params.data = items;
        this._doBatchModifySyncState(params, message, startObj);
        for (var i = 0; i < items.length; i++) {
            items[i].autoSyncEnabled = isOpen;
        }
        grid.batchModifyRowData(null, items);
    },
    batchModifySyncState: function (sender, isOpen) {
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length <= 0) {
            $common.showWarn('请选择需要' + (isOpen ? '开启' : '关闭') + '库存自动同步的网店商品');
            return;
        }
        this.batchModifyStockSyncItems(form, grid, items, isOpen);
    },

    _doBatchModifySyncState: function (params, message, startObj) {
        this.dealQtyBeforeInvoke(params.data);
        var _this = this;
        if (!message) {
            message = '';
        }
        $common.ajax({
            url: '/sale/eshoporder/product/batchModifyStockSyncState',
            data: params,
            router: 'ngp',
            waiting: '正在更新状态,请稍等',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                _this.dealQtyAfterInvoke(params.data);
                if (response.code != 200) {
                    $common.showError(message + (params.isOpen == 1 ? '开启' : '关闭') + '库存自动同步失败' + ((message.length > 0 || params.data.length > 1) ? params.data.length + '条:' : ":") + response.message);

                } else if (response.data && response.data.length > 0) {
                    $common.showError(message + (params.isOpen == 1 ? '开启' : '关闭') + '库存自动同步失败' + ((message.length > 0 || params.data.length > 1) ? params.data.length + '条:' : ":") + response.data);

                } else {
                    $common.showInfo(message + (params.isOpen == 1 ? '开启' : '关闭') + '库存自动同步成功' + ((message.length > 0 || params.data.length > 1) ? params.data.length + '条' : ""));
                }
            }
        });
    },
    _updateItemsSelectedValue: function (items) {
        if (!items || items.length <= 0) {
            return;
        }
        for (var i = 0; i < items.length; i++) {
            if (items[i].selected) {
                items[i].selected = 1;
            } else {
                items[i].selected = 0;
            }
        }
    },
    batchBindDefaultStockSyncRule: function () {
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length <= 0) {
            $common.showWarn('请选择需要使用默认库存同步规则的网店商品');
            return;
        }
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->绑定默认库存同步规则');
        var needUpdateRows = [];
        var skipCount = this.filterUnMappingItems(items, needUpdateRows);

        if (needUpdateRows.length <= 0) {
            $saleUtils._logEndTiming(startObj);
            $common.showWarn('请先完成商品对应再设置库存规则');
            return;
        }
        var message = '';
        if (skipCount > 0) {
            message = '因商品未对应,过滤sku' + skipCount + '条; ';
        }
        this.bindRule(needUpdateRows, 0, '默认库存同步规则', message, startObj);

    },
    filterUnMappingItems: function (items, needUpdateRows) {
        var skipCount = 0;
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            if (!item.ptypeId || item.ptypeId == 0) {
                skipCount++;
                continue;
            }
            needUpdateRows.push(item);
        }
        return skipCount;
    },
    bindRule: function (items, ruleId, ruleName, message, startObj) {
        if (!message) {
            message = '';
        }
        var form = this.get_form();
        var grid = form.grid;
        if (!ruleName) {
            ruleName = ruleId == 0 ? '默认库存同步规则' : '自定义库存同步规则';
        }
        this.dealQtyBeforeInvoke(items);
        var _this = this;
        $common.ajax({
            url: '/sale/eshoporder/product/bindRuleId',
            data: {
                data: items,
                ruleId: ruleId,
                ruleName: ruleName
            },
            router: 'ngp',
            waiting: '请稍等',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                _this.dealQtyAfterInvoke(items);
                if (response.code != 200) {
                    $common.showError(message + 'sku绑定' + ruleName + '失败' + ((message.length > 0 || items.length > 1) ? items.length + '条:' : ":") + response.message);
                } else {
                    if (response.data && response.data.length > 0) {
                        $common.showError(message + 'sku绑定' + ruleName + '失败' + ((message.length > 0 || items.length > 1) ? items.length + '条:' : ":") + response.data);
                    } else {
                        $common.showInfo(message + 'sku绑定' + ruleName + '成功' + ((message.length > 0 || items.length > 1) ? items.length + '条' : ""));
                        if (ruleName == '默认库存同步规则') {
                            ruleName = '(默)网店默认规则';
                        }
                        for (var i = 0; i < items.length; i++) {
                            var item = items[i];
                            item.ruleName = ruleName;
                            var ruleDetail = _this.getRuleById(ruleId, item.otypeId);
                            if (ruleDetail) {
                                item.warehouseStockSyncEnabled = ruleDetail.warehouseStockSyncEnabled;
                                item.eshopMultiStockSyncEnabled = ruleDetail.eshopMultiStockSyncEnabled;
                            }
                        }
                        window.setTimeout(function () {
                            grid.batchModifyRowData(null, items);
                        }, 100);
                    }
                }
            },
            error: function (response) {
                $saleUtils._logEndTiming(startObj);
                $common.showError(message + ruleName + '绑定失败:' + ((message.length > 0 || items.length > 1) ? items.length + '条:' : ":") + response.message);
            }
        });
    },
    doCustomRuleSelected: function (rule) {
        if (!rule) {
            return;
        }
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->绑定自定义库存同步规则');
        var form = this.get_form();
        var grid = form.grid;
        var row = grid.get_selectedRowData();
        var items = [];
        items.push(row);
        this.bindRule(items, rule.id, rule.ruleName, '', startObj);
        this.closePopup();
        grid.focusSelectedRowColumn('autoSyncEnabled');
    },

    batchBindCustomRule: function (sender) {
        var form = this.get_form();
        var grid = form.grid;
        var items = grid.get_selectedItems();
        if (!items || items.length <= 0) {
            $common.showWarn('请选择需要使用自定义库存同步规则的网店商品');
            return;
        }
        var needUpdateItems = [];
        var skipCount = this.filterUnMappingItems(items, needUpdateItems);
        if (needUpdateItems.length <= 0) {
            $common.showWarn('请先完成商品对应再设置库存规则');
            return;
        }
        var message = '';
        if (skipCount > 0) {
            message = '因商品未对应,过滤sku' + skipCount + '条; ';
        }
        var modal = new Sys.UI.Form(form);
        var _this = this;
        modal.add_ok(function (ruleForm) {
            if (!ruleForm || !ruleForm.ruleGrid) {
                return;
            }
            var selectedRule = ruleForm.ruleGrid.get_selectedRowData();
            var startObj = $saleUtils._getStartTiming('网店->网店商品管理->绑定自定义库存同步规则');
            _this.bindRule(needUpdateItems, selectedRule.id, selectedRule.ruleName, message, startObj);
            _this.closePopup();
        })
        modal.showModal("sale/eshoporder/product/StockRuleList.gspx", {
            mode: 'select'
        });
    },

    doSelectUp: function (sender) {
        this._doSelectFrom(sender, true);
    },
    doSelectDown: function (sender) {
        this._doSelectFrom(sender, false);
    },
    _doSelectFrom: function (sender, isUp) {
        var form = this.get_form();
        var grid = form.grid;
        var dataSource = grid.get_dataSource();
        if (!dataSource || dataSource.length <= 0) {
            return;
        }
        var selectedRowIndex = grid.get_selectedRowIndex();
        var itemList = [];
        var start = isUp ? 0 : selectedRowIndex;
        var end = isUp ? selectedRowIndex : dataSource.length - 1;
        for (var i = start; i <= end; i++) {
            dataSource[i].selected = true;
            itemList.push(dataSource[i]);
        }
        grid.batchModifyRowData(null, itemList);
    },

    doBindStockLogDataSource: function (path, params, binData) {
        var form = this.get_form();
        var rowData = form.grid.get_selectedRowData();
        if (!rowData) {
            binData({itemList: [], itemCount: 0});
            return;
        }
        var queryParams = new Object();
        queryParams.platformNumId = rowData.platformNumId;
        queryParams.eshopId = rowData.otypeId;
        queryParams.platformSkuId = rowData.platformSkuId;
        queryParams.syncState = 2;
        var filters = params.queryParams.gridFilter;
        if (filters != null && filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
                var field = filters[i];
                this._buildStockLogFilter(queryParams, field);
            }
        }
        params.queryParams = queryParams;
        var url = "/sale/eshoporder/product/getStockLog";
        $common.ajax({
            url: url,
            data: params,
            router: 'ngp',
            success: function (rsp) {
                if (rsp.code != 200) {
                    binData({itemList: [], itemCount: 0});
                } else {
                    var items = rsp.data.list;
                    var total = rsp.data.total;
                    binData({itemList: items, itemCount: total});
                }
            }
        });

    },

    bindLogGridData: function (path, params, binData) {
        var form = this.get_form();
        var rowData = form.grid.get_selectedRowData();
        if (!rowData) {
            binData({itemList: [], itemCount: 0});
            return;
        }
        var queryParams = new Object();
        queryParams.filterMode = "quickQuery";
        queryParams.platformNumId = rowData.platformNumId;
        queryParams.eshopId = rowData.otypeId;
        queryParams.platformSkuId = rowData.platformSkuId;
        var filters = params.queryParams.gridFilter;
        if (filters != null && filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
                var field = filters[i];
                this._buildLogFilter(queryParams, field);
            }
        }
        params.queryParams = queryParams;
        $common.ajax({
            url: '/sale/eshoporder/relation/queryProductlogList',
            data: params,
            router: 'ngp',
            success: function (queryResponse) {
                if (queryResponse.code != 200) {
                    binData({itemList: [], itemCount: 0});
                } else {
                    var items = queryResponse.data.list;
                    var total = queryResponse.data.total;
                    binData({itemList: items, itemCount: total});
                }
            }
        });
    },

    // bindStockOperationLogData: function (path, params, binData) {
    //     var url = "sale/eshoporder/common/queryStockActionLog"
    //     var form = this.get_form();
    //     var rowData = form.grid.get_selectedRowData();
    //     if (!rowData) {
    //         binData({itemList: [], itemCount: 0});
    //         return;
    //     }
    //     var queryParams = new Object();
    //     queryParams.platformNumId = rowData.platformNumId;
    //     queryParams.eshopId = rowData.otypeId;
    //     queryParams.platformSkuId = rowData.platformSkuId;
    //     var filters = params.queryParams.gridFilter;
    //     if (filters != null && filters.length > 0) {
    //         for (var i = 0; i < filters.length; i++) {
    //             var field = filters[i];
    //             this._buildStockOperationLogFilter(queryParams, field);
    //         }
    //     }
    //     params.queryParams = queryParams;
    //     $common.ajax({
    //         url: url,
    //         data: params,
    //         router: 'ngp',
    //         success: function (queryResponse) {
    //             if (queryResponse.code != 200) {
    //                 binData({itemList: [], itemCount: 0});
    //             } else {
    //                 var items = queryResponse.data.list;
    //                 var total = queryResponse.data.total;
    //                 binData({itemList: items, itemCount: total});
    //             }
    //         }
    //     });
    // },

    _buildLogFilter: function (queryParam, field) {
        if (!field) return;
        if (field.dataField == "opreateTime") {
            queryParam.beginTime = field.value1;
            queryParam.endTime = field.value2;
        } else if (field.dataField == "etypeName") {
            queryParam.etypeName = field.value;
        } else if (field.dataField == "opreateType") {
            queryParam.opreateType = field.value.toString().trim();
        }
    },

    _buildStockLogFilter: function (queryParam, field) {
        if (!field) {
            return;
        }
        if (field.dataField == "syncTime") {
            queryParam.begin = field.value1;
            queryParam.end = field.value2;
        } else if (field.dataField == "etypeName") {
            queryParam.etypeName = field.value;
        } else if (field.dataField == "warehouseCode") {
            queryParam.warehouseCode = field.value;
        } else if (field.dataField == "syncState") {
            queryParam.syncStateFilter = field.value;
        } else if (field.dataField == "syncType") {
            queryParam.syncTypeFilter = field.value;
        }
    },

    _buildStockOperationLogFilter: function (queryParam, field) {
        if (!field) {
            return;
        }
        if (field.dataField == "logTime") {
            queryParam.beginTime = field.value1;
            queryParam.endTime = field.value2;
        } else if (field.dataField == "efullname") {
            queryParam.efullname = field.value;
        }
    },

    closePopup: function () {
        var form = this.get_form();
        form.ruleFlexPop.hide();
    },

    doRuleNameChange: function (sender, args) {
        var form = this.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        var oldVal = args.get_oldValue();
        if (!rowData.ptypeId || rowData.ptypeId == 0 || rowData.ruleName) {
            return;
        }
        var index = args._rowIndex;
        if (oldVal == "(默)网店默认规则") {
            rowData.ruleName = oldVal;
            grid.modifyRowData(index, rowData);
            return;
        }

        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->绑定默认库存同步规则');
        this.bindRule([rowData], 0, '默认库存同步规则', '', startObj);
        grid.focusSelectedRowColumn('autoSyncEnabled');
    },

    doOpenUrl: function (sender) {
        var form = sender.get_form();
        var rowMainData = form.grid.get_selectedRowData();
        if (!rowMainData) {
            return;
        }
        var request = {};
        request.platformSkuInfo = rowMainData.platformNumId;
        request.platformSkuId = rowMainData.platformSkuId;
        request.eshopId = rowMainData.otypeId;
        $common.ajax({
            url: 'sale/eshoporder/order/openUrl',
            data: request,
            router: 'ngp',
            success: function (res) {
                if (res.code != "200" && res.message) {
                    $common.showInfo(res.message);
                    return;
                }
                if (!res.data) {
                    return;
                }
                $common.openwin(res.data);
            }
        });
    },
    changeDisplayTextOrder: function (sender, args) {
        var rowIndex = args.get_rowIndex();
        var rowData = sender.get_form().grid.findRowData(rowIndex);
        var dataField = sender.get_dataField();
        if (!rowData) {
            return; // 空行
        }
        if (dataField == "downloadOrderIntervalDay") {
            if (rowData.downloadOrderIntervalDay && rowData.downloadOrderIntervalDay > 0) {
                if (rowData.downloadOrderIntervalDay.indexOf("天") == -1) {
                    rowData.downloadOrderIntervalDay = rowData.downloadOrderIntervalDay + "天"
                }
            }
            if (rowData.downloadOrderIntervalDay == 0 && rowData.downloadOrderIntervalDay != "") {
                rowData.downloadOrderIntervalDay = "今天";
            }
            if (rowData.downloadOrderIntervalDay == -1) {
                rowData.downloadOrderIntervalDay = "";
            }
            args.set_text(rowData.downloadOrderIntervalDay);
        }
    },
    changeDisplayTextProduct: function (sender, args) {
        var rowIndex = args.get_rowIndex();
        var rowData = sender.get_form().grid.findRowData(rowIndex);
        var dataField = sender.get_dataField();
        if (!rowData) {
            return; // 空行
        }
        if (dataField == "refreshProductIntervalDay") {
            if (rowData.refreshProductIntervalDay && rowData.refreshProductIntervalDay > 0) {
                if (rowData.refreshProductIntervalDay.indexOf("天") == -1) {
                    rowData.refreshProductIntervalDay = rowData.refreshProductIntervalDay + "天"
                }
            }
            if (rowData.refreshProductIntervalDay == 0 && rowData.refreshProductIntervalDay != "") {
                rowData.refreshProductIntervalDay = "今天";
            }
            if (rowData.refreshProductIntervalDay == -1) {
                rowData.refreshProductIntervalDay = "";
            }
            args.set_text(rowData.refreshProductIntervalDay);
        }
    },
    doInitPopup: function (sender) {

        var form = sender.get_form();
        var grid = form.grid;
        var selected = grid.get_selectedRowData();

        //未对应或者按商家编码对应,disable解绑操作
        if (!selected || !selected.ptypeId || selected.ptypeId == 0 || selected.mappingType == "1") {
            form.popClear.set_enabled(false);
        } else {
            form.popClear.set_enabled(true);
        }

        if (selected.disableTips && selected.disableTips.length > 0) {
            form.popOpenSync.set_enabled(false);
            form.popCloseSync.set_enabled(false);
        } else {
            if (selected && selected.autoSyncEnabled) {
                form.popOpenSync.set_enabled(false);
                form.popCloseSync.set_enabled(true);
            } else {
                form.popOpenSync.set_enabled(true);
                form.popCloseSync.set_enabled(false);
            }
        }
    },
    doBtnExportClick: function (sender, args) {
        var form = this.get_form();
        var btn = sender.get_bounds();
        form.skuExportPopup.popup(args);
        form.skuExportPopup._element.style["top"] = (btn.y + 35) + "px";
    },
    doExport: function (sender) {
        var form = sender.get_form();
        var rowCount = form.grid.get_pager().get_itemCount();
        if (rowCount <= 0) {
            $common.alert("没有数据可以导出，请更换查询条件再试");
            return;
        }
        var queryParams = this.get_context('queryParams');
        var exportConfirmForm = new Sys.UI.Form(sender, true);
        var header = this.getExportHeader(sender);
        header.sheetName = "productListSheet";
        var exportRequest = {
            reportName: "sale-export|product-list",
            source: "sale." + form.get_caption(),
            params: JSON.stringify(queryParams.queryParams),
            header: JSON.stringify(header),
            startTime: queryParams.beginTime,
            endTime: queryParams.endTime
        };
        exportConfirmForm.showModal("/exportcenter/ui/ExportConfirm.gspx?title=" + form.get_caption(), exportRequest);
    },

    doExportList: function (sender, args) {
        var form = sender.get_form();
        var exportListForm = new Sys.UI.Form(sender, true);
        exportListForm.showModal("/exportcenter/ui/ExportLogList.gspx?source=" + "sale." + form.get_caption());
    },
    getExportHeader: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var dataObj = {};
        dataObj.reportName = form.get_caption();
        dataObj.masterFields = [];
        $export.initMasterFields(form, grid, dataObj.masterFields);
        dataObj.detailFields = [];
        Sys.UI._Exporter.initDetailFields(grid, dataObj.detailFields);
        return dataObj;
    },
    downloadMainImage: function (sender) {
        var form = this.get_form();
        var _this = this;
        var selectItems = form.grid.get_selectedItems();
        if (selectItems && selectItems.length > 0) {
            var items = this.buildParmItems(selectItems);
            $common.ajax({
                url: "sale/eshoporder/relation/downloadMainImage",
                data: items,
                type: 'post',
                router: 'ngp',
                success: function (res) {
                    if (res) {
                        var taskId = res.data;
                        var obj = new Object();
                        obj.title = "下载网店商品图片到商品图片";
                        obj.processId = taskId;
                        var popForm = new Sys.UI.Form(sender, 0, 0);
                        popForm.set_params(obj);
                        popForm.showModal("sale/eshoporder/eshopproduct/EshopCreatePtypeProgressModal.gspx");
                        popForm.add_closed(function (pop) {
                            form.grid.get_pager().refresh();
                        });
                    }
                }
            });
        } else {
            $common.showError("请选择要下载网店商品主图做商品主图的网店商品");
        }
    },
    buildParmItems: function (selectedItems) {
        var result = [];
        if (null == selectedItems || selectedItems.length === 0) {
            return selectedItems;
        }
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            var newItem = {}
            newItem.eshopId = item.otypeId;
            newItem.ptypeId = item.ptypeId;
            newItem.platformNumId = item.platformNumId;
            newItem.platformSkuId = item.platformSkuId;
            //后端只会处理hasProperties = false 的商品
            newItem.hasProperties = false;
            newItem.skuId = item.skuId;
            newItem.platNameFilterStr = item.platformFullname;
            newItem.platformPropertiesName = item.platformPropertiesName;
            newItem.picUrl = item.platformPicUrl;
            newItem.pcategory = item.pcategory;
            newItem.platformPicUrl = item.platformPicUrl;
            newItem.bind = item.bind;
            result.push(newItem);
        }
        return result;
    },
    autoCreateCombo: function (sender) {
        var form = sender.get_form();
        $eshoppower.checkPowerFalse("baseinfo.combo.add");
        var grid = form.grid;
        var _this = this;
        var selectedItems = grid.get_selectedItems();
        var paramItems = this.buildSelectedItems(selectedItems);
        if (selectedItems.length < 1) {
            $common.showInfo("请先勾选需要智能生成套餐的网店商品行");
            return;
        }
        $common.ajax({
            url: "sale/eshoporder/relation/AutoCreateCombo",
            waiting: '正在执行增操作，请稍后...',
            data: paramItems,
            type: 'post',
            router: 'ngp', success: function (addResult) {
                if (addResult.code != "200" && addResult.message) {
                    $common.showError(addResult.message)
                } else if (addResult.data && addResult.data.code != "200" && addResult.data.message) {
                    $common.showError(addResult.data.message)
                } else {
                    $common.showOk('创建成功', {timer: 2500});
                    _this.get_form().grid.get_pager().refresh();
                }
            }
        });
    },
    buildSelectedItems: function (selectedItems) {
        var result = [];
        if (null == selectedItems || selectedItems.length === 0) {
            return selectedItems;
        }
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            var newItem = {}
            newItem.eshopId = item.otypeId;
            newItem.ptypeId = item.ptypeId;
            newItem.profileId = item.profileId;
            newItem.platformNumId = item.platformNumId;
            newItem.platformSkuId = item.platformSkuId;
            newItem.hasProperties = item.hasProperties;
            newItem.skuId = item.skuId;
            newItem.platNameFilterStr = item.platformFullname;
            newItem.platformPropertiesName = item.platformPropertiesName;
            newItem.picUrl = item.platformPicUrl;
            newItem.pcategory = item.pcategory;
            newItem.platformPicUrl = item.platformPicUrl;
            newItem.bind = item.bind;
            newItem.mappingType = item.mappingType;
            newItem.price = item.platformPrice;
            newItem.platXcode = item.platformXcode;
            newItem.uniqueId = item.uniqueId;
            result.push(newItem);
        }
        return result;
    },
    _doFillKtypeNames: function (list) {
        var ktypeList = this.get_context("ktypeList");
        if (!ktypeList || !list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.ktypeIds || item.ktypeIds.length == 0) {
                continue;
            }
            var ktypeNames = "";
            for (var j = 0; j < ktypeList.length; j++) {
                if (item.ktypeIds.indexOf(ktypeList[j].id) > -1) {
                    ktypeNames += ktypeList[j].fullname + ",";
                }
            }
            if (ktypeNames.length > 0) {
                item.ktypeNames = ktypeNames.substr(0, ktypeNames.length - 1);
            }
        }
    },
    _rebuildFormula: function (list) {
        if (!list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.calculate) {
                continue;
            }
            item.formula = this.buildFormulaFromCalculate(item.calculate);
        }
    },
    buildFormulaFromCalculate: function (calculate) {
        if (!calculate) {
            return;
        }
        return "可销售库存*" + math.round(calculate.percentage, 2) + "%"
            + this._getMode(calculate.calculateMode) + math.round(calculate.ruleCalQty, 2);
    },
    _getMode: function (mode) {
        var result;
        switch (mode + "") {
            case "1":
                result = "-";
                break;
            case "2":
                result = "*";
                break;
            case "3":
                result = "/";
                break;
            default:
                result = "+";
                break;
        }
        return result;
    },
    doCellRendering: function (sender, args) {
        var rowData = args.get_rowData();
        var column = args.get_column();
        var dataField = column.get_dataField();

        if (dataField == 'autoSyncEnabled') {
            if (!rowData || !rowData.autoSyncEnabled) {
                args.set_fontColor('black');
            } else {
                args.set_fontColor('#3FBB00');
            }
        }

        if (dataField != 'qty' && dataField != 'autoSyncEnabled' && dataField != 'ruleName') {
            return;
        }
        rowData.disableTips = '';
        var tips = "";
        var isSupportSyncEshop = this.checkIsSupportSyncShoptype(this.get_context('generalStockSyncShopTypes'), rowData.eshopType);
        // var isWarehouseSupportSyncEshop = this.checkIsSupportSyncShoptype(this.get_context('warehouseStockSyncShopTypes'), rowData.eshopType);

        //网店不支持
        if (!isSupportSyncEshop) {
            tips = "该网店不支持库存同步";
            args.set_cssClass('disabled')
            args.set_style("background-color:#f6f6f6");
            rowData.disableTips = tips;
            rowData.qty = '';
            return;
        }

        //绑定状态
        if (!rowData.ptypeId || rowData.ptypeId == 0) {
            tips = "未绑定对应关系的商品不支持库存同步";
            args.set_cssClass('disabled')
            args.set_style("background-color:#f6f6f6");
            rowData.disableTips = tips;
            rowData.qty = '';
            return;
        }

        //权限
        var stockSyncEnabled = this.get_context('stockSyncEnabled');
        if (!stockSyncEnabled) {
            tips = "未配置库存同步权限，不支持库存同步";
            args.set_cssClass('disabled')
            args.set_style("background-color:#f6f6f6");
            rowData.disableTips = tips;
            rowData.qty = '';
            return;
        }
    },
    checkIsSupportSyncShoptype: function (shopTypes, shopType) {
        if (!shopTypes || shopTypes.length == 0) {
            return true;
        }
        for (var i = 0; i < shopTypes.length; i++) {
            if (shopTypes[i] == shopType) {
                return true;
            }
        }
        return false;
    },
    dealQtyBeforeInvoke: function (dataList) {
        if (!dataList || dataList.length == 0) {
            return;
        }
        for (var i = 0; i < dataList.length; i++) {
            var item = dataList[i];
            if (item.qty == '') {
                item.qty = null;
            }
        }
    },
    dealQtyAfterInvoke: function (dataList) {
        if (!dataList || dataList.length == 0) {
            return;
        }
        for (var i = 0; i < dataList.length; i++) {
            var item = dataList[i];
            if (item.disableTips && item.disableTips.length > 0) {
                item.qty = '';
            }
        }
    },
    dispose: function () {
        sale.product.listAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.listAction.registerClass('sale.product.listAction', Sys.UI.PageAction);