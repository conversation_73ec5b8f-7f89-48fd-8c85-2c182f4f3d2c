Type.registerNamespace('sale.product');

sale.product.StockDefaultRuleListAction = function () {
    sale.product.StockDefaultRuleListAction.initializeBase(this);
};

sale.product.StockDefaultRuleListAction.prototype = {
    context: function (cb) {
        var pageParams = this.get_pageParams();
        var title = "网店默认库存同步规则设置";
        var ktypeList = pageParams.ktypeList;
        if (!ktypeList || ktypeList.length == 0) {
            var response = $common.ajaxSync({
                url: 'sale/eshoporder/basic/ktypeList',
                type: 'post',
                router: 'ngp'
            });
            ktypeList:response.data;
        }
        if (pageParams && pageParams.title && pageParams.title.length > 0) {
            title = pageParams.title;
        }
        cb({
            title: title,
            ktypeList: ktypeList
        });
    },
    initialize: function StockDefaultRuleListAction$initialize() {
        sale.product.StockDefaultRuleListAction.callBaseMethod(this, 'initialize');
        this.get_form().ruleGrid.get_pager().refresh();
    },

    doBindPagerDataSource: function (path, params, binData) {
        var url = '/sale/eshoporder/product/defaultRuleList';
        params.queryParams = this.buildQueryParams(params);
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->默认库存同步规则设置查询');
        var that = this;
        $common.ajax({
            url: url,
            data: params,
            router: 'ngp',
            waiting: '正在查询数据,请稍等',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                if (response.code != 200) {
                    $common.alert(response.message);
                    binData({itemList: [], itemCount: 0});
                    return;
                }
                var data = response.data;
                if (!data || data.total == 0) {
                    binData({itemList: [], itemCount: 0});
                } else {
                    that._doFillKtypeNames(data.list);
                    that._rebuildFormula(data.list);
                    binData({itemList: data.list, itemCount: data.total});
                }
            }
        });
    },
    buildQueryParams: function (params) {
        var queryParams = {};
        if (!params || !params.queryParams || !params.queryParams.gridFilter) {
            return queryParams;
        }
        for (var i = 0; i < params.queryParams.gridFilter.length; i++) {
            var item = params.queryParams.gridFilter[i];
            var dataField = item.dataField;
            var dataValue = item.value;
            if (!dataValue || dataValue == '') {
                if (dataField == 'autoSyncEnabled' && dataValue == false) {
                    queryParams[dataField] = false;
                }
                continue;
            }
            queryParams[dataField] = dataValue;
        }
        return queryParams;
    },
    _doFillKtypeNames: function (list) {
        var ktypeList = this.get_context("ktypeList");
        if (!ktypeList || !list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.ktypeIds || item.ktypeIds.length == 0) {
                continue;
            }
            var ktypeNames = "";
            for (var j = 0; j < ktypeList.length; j++) {
                if (item.ktypeIds.indexOf(ktypeList[j].id) > -1) {
                    ktypeNames += ktypeList[j].fullname + ",";
                }
            }
            if (ktypeNames.length > 0) {
                item.ktypeNames = ktypeNames.substr(0, ktypeNames.length - 1);
            }
        }
    },
    _rebuildFormula: function (list) {
        if (!list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.calculate) {
                continue;
            }
            item.formula = this.buildFormulaFromCalculate(item.calculate);
        }
    },
    buildFormulaFromCalculate: function (calculate) {
        if (!calculate) {
            return;
        }
        return "可销售库存*" + math.round(calculate.percentage, 2) + "%"
            + this._getMode(calculate.calculateMode) + math.round(calculate.ruleCalQty, 2);
    },
    _getMode: function (mode) {
        var result;
        switch (mode + "") {
            case "1":
                result = "-";
                break;
            case "2":
                result = "*";
                break;
            case "3":
                result = "/";
                break;
            default:
                result = "+";
                break;
        }
        return result;
    },
    doCellRender: function (sender, args) {
    },

    doDblClick: function (sender) {
        var form = sender.get_form();
        var rowData = form.ruleGrid.get_selectedRowData();
        this._showEditRuleForm(form, rowData);
    },
    doChangeFilter: function (sender) {
        var grid = sender.get_form().ruleGrid;
        var allowed = grid.get_allowFilter();
        grid.set_allowFilter(!allowed);
    },
    doAutoSyncFilterRendering: function (sender, args) {
        var props = {
            items: [{value: true, text: '开启'}, {value: false, text: '关闭'}]
        };
        args.set_properties(props);
    },
    doOperationClick: function (sender) {
        var form = sender.get_form();
        var rowData = form.ruleGrid.get_selectedRowData();
        this._showEditRuleForm(form, rowData);
    },
    _showEditRuleForm: function (form, rowData) {
        var params = {
            ktypeList: this.get_context("ktypeList"),
            mode: 'default',
            rule: rowData
        };
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/EditRuleForm.gspx", params);
        modal.add_close(function (sender, args) {
            form.ruleGrid.get_pager().refresh();
        });
    },
    doAutoSyncEnableChanged: function (sender, args) {
        sender.set_enabled(false);
        $common.showWaiting(window);
        var form = sender.get_form();
        var rowData = form.ruleGrid.get_selectedRowData();
        var isOpen = rowData.autoSyncEnabled;

        var url = '/sale/eshoporder/product/updateAutoSyncStockEnabled';
        var response = $common.ajaxSync({
            url: url,
            data: rowData,
            type: 'post',
            router: 'ngp'

        })
        $common.hideWaiting(window);

        if (response.code != 200) {
            $common.showError(("库存自动同步" + (isOpen ? "开启" : "关闭") + "失败。") + response.message ? response.message : "");
            args.set_cancel(true);
        } else {
            if (response.data && response.data.length > 0) {
                $common.showError(("库存自动同步" + (isOpen ? "开启" : "关闭") + "失败。") + response.data);
                args.set_cancel(true);
            } else {
                $common.showOk("库存自动同步" + (isOpen ? "开启" : "关闭") + "成功");
                sender.get_grid().modifyCellValue(args.get_rowIndex(), sender, args.get_value());
            }
        }
        sender.set_enabled(true);
    },
    doRuleGridSelectionChanged: function (sender) {
        var form = sender.get_form();
        var grid = form.ruleGrid;
        var data = grid.get_selectedRowData();
        if (!data) {
            return;
        }
        var queryParams = {
            rowData: data,
            objectId: data.id
        }
        form.rule_log_ctrl.logGrid.get_pager().refresh(queryParams);
    },
    dispose: function () {
        sale.product.StockDefaultRuleListAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.StockDefaultRuleListAction.registerClass('sale.product.StockDefaultRuleListAction', Sys.UI.PageAction);
