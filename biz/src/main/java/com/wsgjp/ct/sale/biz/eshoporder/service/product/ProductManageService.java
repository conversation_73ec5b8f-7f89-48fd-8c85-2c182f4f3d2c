package com.wsgjp.ct.sale.biz.eshoporder.service.product;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.google.common.collect.Lists;
import com.wsgjp.ct.baseinfo.core.service.ptype.PtypeXcodeService;
import com.wsgjp.ct.common.enums.core.enums.ProductMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.redis.process.message.bll.RedisMessageUtil;
import com.wsgjp.ct.sale.biz.bifrost.entity.BaseInfoLog;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopProductService;
import com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.common.enums.ProcessMessageType;
import com.wsgjp.ct.sale.biz.common.util.CurrentEtypeUtil;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypeCombo;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.NameConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.EshopProcess;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreateComboParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreatePtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.product.EshopUpdateProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.product.QueryEshopSkuListRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ComboSaveResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.MultiStockSyncDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.QueryStockRuleParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockRuleDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.StockBuildService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.ProductManageUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyXcodeRequest;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductModifyResponse;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductModifyFeature;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class ProductManageService {

    private final EshopProductNewMapper mapper;
    private final EshopOrderBaseInfoMapper baseInfoMapper;
    private final StockRuleMapper ruleMapper;
    private final EshopService eshopService;
    private final BifrostEshopProductService eshopProductService;
    private final PtypeSaver ptypeSaver;
    private final SaleBizConfig config;
    private final ServiceConfig serviceConfig;
    private final EshopProductMarkMapper markMapper;
    private final EshopProductMarkBigDataMapper markBigDataMapper;
    private final EshopSaleOrderService orderService;
    private final EshopProductSaver productSaver;

    private final PtypeXcodeService ptypeXcodeService;

    private final CurrentEtypeUtil etypeUtil;

    private final StockBuildService stockSvc;


    private static final String NO_MARK_DESCRIPTION = "无标记";


    private static final Logger logger = LoggerFactory.getLogger(ProductManageService.class);

    public ProductManageService(EshopProductNewMapper mapper,
                                EshopOrderBaseInfoMapper baseInfoMapper, StockRuleMapper ruleMapper,
                                EshopService eshopService,
                                BifrostEshopProductService eshopProductService,
                                PtypeSaver ptypeSaver,
                                SaleBizConfig config,
                                ServiceConfig serviceConfig, EshopProductMarkMapper markMapper,
                                EshopProductMarkBigDataMapper markBigDataMapper,
                                EshopSaleOrderService orderService, EshopProductSaver productSaver, CurrentEtypeUtil etypeUtil, StockBuildService stockSvc, PtypeXcodeService ptypeXcodeService) {
        this.mapper = mapper;
        this.baseInfoMapper = baseInfoMapper;
        this.ruleMapper = ruleMapper;
        this.eshopService = eshopService;
        this.eshopProductService = eshopProductService;
        this.ptypeSaver = ptypeSaver;
        this.config = config;
        this.serviceConfig = serviceConfig;
        this.markMapper = markMapper;
        this.markBigDataMapper = markBigDataMapper;
        this.orderService = orderService;
        this.productSaver = productSaver;
        this.ptypeXcodeService = ptypeXcodeService;
        this.etypeUtil = etypeUtil;
        this.stockSvc = stockSvc;
    }

    public PageResponse<EshopProductSkuPageData> querySkuList(PageRequest<QueryEshopSkuListRequest> params, boolean isExport) {
        try {
            PageResponse<EshopProductSkuPageData> response = new PageResponse<>();
            PageDevice.initPageWithCount(params, false);
            List<EshopProductSkuPageData> dataList = mapper.querySkuList(params.getQueryParams());
            if (CollectionUtils.isEmpty(dataList)) {
                return PageDevice.readPage(dataList);
            }
            if (isExport) {
                //导出需要同步查询标记
                buildMarkByPageData(dataList, true);
                //导出获取库存,首板不获取分仓和时效
                dataList = stockSvc.queryStockByPageData(dataList, false);
            }
            initResponse(response, params, dataList);
            return response;
        } catch (Exception ex) {
            throw new RuntimeException("网店商品管理查询SKU列表报错：" + ex.getMessage(), ex);
        }
    }

    public List<EshopProductSkuPageData> querySkuList(QueryEshopSkuListRequest request) {
        try {
            List<EshopProductSkuPageData> dataList = request.getDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                throw new RuntimeException("参数dataList为空，查询失败");
            }
            Map<String, Integer> indexMap = new HashMap<>();
            for (EshopProductSkuPageData data : dataList) {
                indexMap.put(data.getUniqueId(), data.getIndex());
            }
            List<String> uniqueIds = new ArrayList<>(indexMap.keySet());
            request.setUniqueIds(uniqueIds);
            List<EshopProductSkuPageData> skuPageData = mapper.querySkuList(request);
            for (EshopProductSkuPageData rowData : skuPageData) {
                String uniqueId = rowData.getUniqueId();
                Integer index = indexMap.get(uniqueId);
                rowData.setIndex(index);
            }
            return skuPageData;
        } catch (Exception ex) {
            throw new RuntimeException("网店商品管理查询SKU列表报错：" + ex.getMessage(), ex);
        }
    }

    private void initResponse(PageResponse<EshopProductSkuPageData> response,
                              PageRequest<QueryEshopSkuListRequest> params,
                              List<EshopProductSkuPageData> dataList) {
        response.setPageIndex(params.getPageIndex());
        response.setPageSize(params.getPageSize());
        response.setTotal(dataList.size());
        response.setList(dataList);
    }

    public int querySkuListCount(QueryEshopSkuListRequest query) {
        return mapper.querySkuListCount(query);
    }

    public List<StockSyncRule> queryDefaultRule(QueryStockRuleParameter parameter) {
        return ruleMapper.queryDefaultRules(parameter);
    }

    public void modifyPlatformXcode(EshopProductSkuPageData pageData) {
        try {
            BigInteger otypeId = pageData.getOtypeId();
            EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), otypeId);
            EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
            ModifyXcodeRequest xcodeRequest = buildModifyXcodeRequest(pageData, systemParams);
            ProductModifyResponse resp = eshopProductService.modifyProductXcode(xcodeRequest);
            if (resp.getSuccess()) {
                doUpdateLocalXcode(Collections.singletonList(pageData), ProductOperateLogType.ChangeXCode);
            } else {
                throw new RuntimeException("调用接口报错:" + resp.getMessage());
            }
        } catch (Exception ex) {
            throw new RuntimeException("修改网店商品商家编码报错:" + ex.getMessage(), ex);
        }
    }

    public void modifyLocalXcode(EshopProductSkuPageData pageData) {
        try {
            com.wsgjp.ct.baseinfo.core.dao.entity.PtypeXcode ptypeXcode = new com.wsgjp.ct.baseinfo.core.dao.entity.PtypeXcode();
            ptypeXcode.setPtypeId(pageData.getPtypeId());
            ptypeXcode.setSkuId(pageData.getSkuId());
            ptypeXcode.setXcode(pageData.getXcode());
            ptypeXcode.setDefaulted(true);
            ptypeXcode.setUnitId(pageData.getUnitId());
            ptypeXcode.setProfileId(CurrentUser.getProfileId());
            ptypeXcodeService.saveInfo(ptypeXcode);
        } catch (Exception ex) {
            logger.error("修改系统商家编码报错:" + ex.getMessage(), ex);
            throw new RuntimeException(String.format("修改修改系统商家编码失败:%s", ex.getMessage()));
        }
        EshopProductSkuMapping skuMapping = pageData.toSkuMapping();
        mapper.batchInsertOrUpdateProductSkuMapping(Collections.singletonList(skuMapping));
        mapper.updateSkuExpandMappingType(CurrentUser.getProfileId(), Collections.singletonList(pageData.getUniqueId()), MappingType.XCODEMAPPING.getCode());
    }

    private ModifyXcodeRequest buildModifyXcodeRequest(EshopProductSkuPageData data, EshopSystemParams systemParams) {
        ModifyXcodeRequest req = new ModifyXcodeRequest();
        req.setMainProduct(!data.isHasProperties());
        req.setShopId(data.getOtypeId());
        req.setNewXcode(data.getPlatformXcode());
        req.setPlatformMainXcode(data.getPmXcode());
        req.setPlatformNumId(data.getPlatformNumId());
        req.setPlatformSkuId(data.getPlatformSkuId());
        req.setPlatformProperties(data.getPlatformProperties());
        req.setPlatformPropertiesName(data.getPlatformPropertiesName());
        req.setSystemParams(systemParams);
        req.setShopType(data.getEshopType());
        return req;
    }

    public void generateProductXcode(EshopGenerateProductXcodeRequest request, ProcessLoggerImpl pLogger) {
        if (CollectionUtils.isEmpty(request.getData())) {
            pLogger.appendMsg("没有需要修改的商家编码");
            pLogger.doFinish();
            return;
        }
        int totalDataList = request.getData().size();
        pLogger.appendMsg("开始修改商家编码");
        pLogger.appendMsg("正在更新线上商家编码,已更新0条");
        Map<BigInteger, List<EshopProductSkuPageData>> dataMap =
                request.getData().stream().collect(Collectors.groupingBy(EshopProductSkuPageData::getOtypeId));
        AtomicInteger count = new AtomicInteger();
        AtomicInteger failedCount = new AtomicInteger();
        AtomicInteger successCount = new AtomicInteger();
        ThreadPool taskPool = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_XCODE_CREATE.getName());
        //清空原请求的数据
        request.setData(new ArrayList<>());
        List<String> errorList = new ArrayList<>();
        AtomicInteger taskCount = new AtomicInteger();
        taskPool.executeAsyncTaskList(dataList -> {
            try {
                int originalCount = dataList.size();
                EshopInfo eshopInfoById = eshopService.getEshopInfoById(CurrentUser.getProfileId(), dataList.get(0).getOtypeId());
                boolean featureSupported = EshopUtils.isFeatureSupported(EshopProductModifyFeature.class, eshopInfoById.getEshopType());
                if (!featureSupported) {
                    ProductManageUtil.appendNotSupportMsg(pLogger, eshopInfoById, failedCount.addAndGet(dataList.size()));
                    return;
                }
                List<EshopProductSkuPageData> updatedList = new ArrayList<>();
                dataList = generateNewXcodeByRules(request, dataList, errorList);
                if (CollectionUtils.isNotEmpty(errorList)) {
                    RedisMessageUtil.set(String.format("%s%s", "redis_process_failed_count_",
                                    pLogger.getLoggerKey()),
                            String.valueOf(failedCount.addAndGet(originalCount - dataList.size())),
                            60000);
                    RedisMessageUtil.set(String.format("%s%s",
                                    EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE, pLogger.getLoggerKey()),
                            String.valueOf((int) Math.floor((double) count
                                    .addAndGet(originalCount - dataList.size()) * 100 / totalDataList)), 60000);

                }
                if (CollectionUtils.isEmpty(dataList)) {
                    return;
                }

                List<EshopProductMark> productMarks = new ArrayList<>();
                boolean needQueryProductMarks = needQueryProductMarks(eshopInfoById.getEshopType());
                if (needQueryProductMarks) {
                    List<String> numIdList = dataList.stream()
                            .map(EshopProductSkuPageData::getPlatformNumId).collect(Collectors.toList());
                    productMarks = mapper.queryEshopProductMarksByNumIds(CurrentUser.getProfileId(),
                            eshopInfoById.getOtypeId(),
                            numIdList);
                }
                ModifyXcodeRequest req;
                EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfoById);
                for (EshopProductSkuPageData data : dataList) {
                    try {
                        req = buildModifyXcodeRequest(data, systemParams);
                        boolean isNeedMark = needQueryProductMarks
                                && productMarks.stream().anyMatch(mark -> mark.getPlatformNumId().equals(data.getPlatformNumId())
                                && mark.getMarkCode() == EshopProductMarkEnum.COMMUNITY_GROUP_BUY.getCode()
                                && (StringUtils.isEmpty(mark.getPlatformSkuId())
                                || "0".equals(mark.getPlatformSkuId())
                                || mark.getPlatformSkuId().equals(data.getPlatformSkuId())));
                        req.setProductMarkEnumList(isNeedMark
                                ? Collections.singletonList(ProductMarkEnum.COMMUNITY_GROUP_BUY)
                                : new ArrayList<>());
                        ProductModifyResponse resp = eshopProductService.modifyProductXcode(req);
                        if (!resp.getSuccess()) {
                            String errorMsg = ProductManageUtil.buildErrorMessage(data, resp.getMessage());
                            errorList.add(errorMsg);
                            failedCount.addAndGet(1);
                        } else {
                            updatedList.add(data);
                            successCount.addAndGet(1);
                        }
                    } catch (Exception ex) {
                        failedCount.addAndGet(1);
                    } finally {
                        pLogger.modifyMsg(String.format("正在更新线上商家编码,已经更新%d条", count.addAndGet(1)), 60000);
                        RedisMessageUtil.set(String.format("%s%s",
                                        EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE, pLogger.getLoggerKey()),
                                String.valueOf((int) Math.floor((double) count.get() * 100 / totalDataList)), 60000);
                        RedisMessageUtil.set(String.format("%s%s", "redis_process_success_count_",
                                pLogger.getLoggerKey()), String.valueOf(successCount.get()), 60000);
                        RedisMessageUtil.set(String.format("%s%s", "redis_process_failed_count_",
                                pLogger.getLoggerKey()), String.valueOf(failedCount.get()));
                    }
                }
                doUpdateLocalXcode(updatedList, ProductOperateLogType.BatchChangeOnlineXCode);
            } catch (Exception ex) {
                logger.error("账套{}生成商家编码失败:{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
                pLogger.appendMsg("生成商家编码失败:" + ex.getMessage());
            } finally {
                if (taskCount.addAndGet(1) >= dataMap.size()) {
                    pLogger.modifyMsg(String.format("正在更新线上商家编码,已经更新%d条", totalDataList));
                    //打印错误信息
                    if (CollectionUtils.isNotEmpty(errorList)) {
                        for (String error : errorList) {
                            pLogger.appendMsg(error);
                        }
                    }
                    pLogger.appendMsg("生成商家编码完成");
                    pLogger.doFinish();
                }
            }
        }, new ArrayList<>(dataMap.values()));
    }

    private boolean needQueryProductMarks(ShopType eshopType) {
        if (StringUtils.isEmpty(config.getNeedBuildProductMarkShopList())) {
            return false;
        }
        return Arrays.asList(config.getNeedBuildProductMarkShopList().split(","))
                .contains(String.valueOf(eshopType.getCode()));
    }

    public List<EshopProductSkuPageData> generateNewXcodeByRules(EshopGenerateProductXcodeRequest request,
                                                                 List<EshopProductSkuPageData> dataList,
                                                                 List<String> errorList) {
        if (UploadRuleType.AUTO.equals(request.getRule())) {
            return autoCreateXcodeByRule(request, dataList, errorList);
        }
        List<EshopProductSkuPageData> newXcodeList = new ArrayList<>();
        for (EshopProductSkuPageData skuData : dataList) {
            if (request.getRule().equals(UploadRuleType.EQUALBARCODE) && !skuData.isBind()) {
                String errorMsg = ProductManageUtil.buildErrorMessage(skuData, "未对应不能根据条码/套餐条码修改编码，自动跳过");
                errorList.add(errorMsg);
                continue;
            }
            skuData.setOldPlatXCode(skuData.getPlatformXcode());
            String newPlatXcode = getNewXcodeByRule(request, skuData);
            //如果生成的线上xcode是空，那不同步给线上
            if (StringUtils.isNotEmpty(newPlatXcode)) {
                if (newPlatXcode.length() <= 50) {
                    skuData.setPlatformXcode(newPlatXcode);
                    newXcodeList.add(skuData);
                } else {
                    String errorMsg = ProductManageUtil.buildErrorMessage(skuData, String.format("生成的商家编码【%s】过长，自动跳过", newPlatXcode));
                    errorList.add(errorMsg);
                }
            } else {
                String errorMsg = ProductManageUtil.buildErrorMessage(skuData, "生成的商家编码为空，自动跳过");
                errorList.add(errorMsg);
            }
        }
        return newXcodeList;
    }

    private String getNewXcodeByRule(EshopGenerateProductXcodeRequest request, EshopProductSkuPageData sonItem) {
        String newXcode = "";
        switch (request.getRule().getCode()) {
            case 0:
                newXcode = buildXcodeByRuleZero(request, sonItem);
                break;
            case 1:
                newXcode = (sonItem.getXcode() == null || sonItem.getXcode().isEmpty()) ? "" : sonItem.getXcode().trim();
                break;
            default:
                newXcode = (sonItem.getPlatformSkuId() == null || sonItem.getPlatformSkuId().isEmpty()) ? sonItem.getPlatformNumId() : sonItem.getPlatformSkuId().trim();
                break;
        }
        return newXcode;
    }

    private String buildXcodeByRuleZero(EshopGenerateProductXcodeRequest request,
                                        EshopProductSkuPageData sonItem) {
        String connectString = ProductManageUtil.getXcodeConnectString(request.getRuleSettleSplit());
        StringBuilder sb = new StringBuilder();
        boolean xcodeN = sonItem.getPmXcode() == null || sonItem.getPmXcode().isEmpty();
        boolean propertiyN = sonItem.getPlatformPropertiesName() == null || sonItem.getPlatformPropertiesName().isEmpty();
        if (request.isUsePlatXcode()) {
            sb.append(sonItem.getPmXcode() == null ? "" : sonItem.getPmXcode());
        }
        if (!xcodeN) {
            sonItem.setPmXcode(sonItem.getPmXcode().trim());
        }
        if (!propertiyN) {
            sonItem.setPlatformPropertiesName(sonItem.getPlatformPropertiesName().trim());
        }
        if (request.isUsePlatProperties()) {
            if (request.isUsePlatXcode()) {
                if (xcodeN || propertiyN) {
                    sb.append(String.format("%s", sonItem.getPlatformPropertiesName() == null ? "" : sonItem.getPlatformPropertiesName()));
                } else {
                    sb.append(String.format("%s%s", connectString, sonItem.getPlatformPropertiesName()));
                }
            } else {
                sb.append(sonItem.getPlatformPropertiesName() == null ? "" : sonItem.getPlatformPropertiesName());
            }
        }
        return sb.toString();
    }

    private List<EshopProductSkuPageData> autoCreateXcodeByRule(EshopGenerateProductXcodeRequest request,
                                                                List<EshopProductSkuPageData> dataList,
                                                                List<String> errorList) {
        String connectString = ProductManageUtil.getXcodeConnectString(request.getAutoSplit());
        List<EshopProductSkuPageData> succList = new ArrayList<>();
        List<EshopProductSkuPageData> genList = dataList.stream().filter(x -> !x.isHasProperties()).collect(Collectors.toList());
        long startNum = request.getStartNum();
        for (EshopProductSkuPageData sonItem : genList) {
            try {
                buildNewXcode(request.getPre(), connectString, startNum, sonItem);
                startNum++;
                succList.add(sonItem);
            } catch (Exception ex) {
                String errorMsg = ProductManageUtil.buildErrorMessage(sonItem, ex.getMessage());
                errorList.add(errorMsg);
            }
        }
        return succList;
    }

    private void buildNewXcode(String pre,
                               String connStr,
                               long numberInc,
                               EshopProductSkuPageData sonItem) {
        String newXcode = "";
        if ("".equals(pre)) {
            newXcode = String.valueOf(numberInc);
        } else {
            newXcode = String.format("%s%s%s", pre, connStr, numberInc);
        }
        sonItem.setOldPlatXCode(sonItem.getPlatformXcode());
        sonItem.setPlatformXcode(newXcode);
    }


    private void doUpdateLocalXcode(List<EshopProductSkuPageData> updatedSkuData, ProductOperateLogType logType) {
        if (CollectionUtils.isEmpty(updatedSkuData)) {
            return;
        }
        batchUpdateProductSkuMappingXcode(updatedSkuData, logType);
        autoRelationByXcode(updatedSkuData, logType);
        List<EshopProductSkuPageData> mainPtypes = updatedSkuData.stream()
                .filter(data -> !data.isHasProperties()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mainPtypes)) {
            batchUpdateProductMappingXcode(CurrentUser.getProfileId(), mainPtypes);
        }
    }

    private void autoRelationByXcode(List<EshopProductSkuPageData> dataList, ProductOperateLogType logType) {
        List<EshopProductSkuPageData> xcodeMappings = dataList.stream().
                filter(x -> x.getMappingType() == MappingType.XCODEMAPPING.getCode() && StringUtils.isNotEmpty(x.getPlatformXcode())).
                collect(Collectors.toList());
        if (CollectionUtils.isEmpty(xcodeMappings)) {
            return;
        }
        if (xcodeMappings.size() < 10 || !logType.equals(ProductOperateLogType.BatchChangeOnlineXCode)) {
            doAutoRelationByXcode(xcodeMappings, logType);
            return;
        }
        ThreadPool pool = ThreadPoolFactory.build(NameConstantEnum.SYNC_PRODUCT_RELATION_AND_MARK.getName());
        pool.executeAsync(x -> {
            doAutoRelationByXcode(xcodeMappings, logType);
        }, null);
    }

    private void doAutoRelationByXcode(List<EshopProductSkuPageData> xcodeMappings, ProductOperateLogType logType) {
        List<EshopProductSkuMapping> skuList = xcodeMappings.stream().map(EshopProductSkuPageData::toSkuMapping).collect(Collectors.toList());
        Map<String, EshopProductSkuMapping> mappingMap = productSaver.buildLocalSkuMap(skuList);
        Map<String, List<PtypeXcode>> listMap = productSaver.buildLocalXcodeMap(skuList);
        for (EshopProductSkuMapping sku : skuList) {
            String platformXcode = sku.getPlatformXcode();
            if (!listMap.containsKey(platformXcode)) {
                continue;
            }
            EshopProductSkuMapping localMapping = mappingMap.get(sku.getUniqueId());
            if (localMapping == null) {
                continue;
            }
            productSaver.logSkuChangeInfo(sku, localMapping, listMap, logType);
        }
        mapper.batchInsertOrUpdateProductSkuMapping(skuList);
    }

    private void batchUpdateProductMappingXcode(BigInteger profileId, List<EshopProductSkuPageData> mainPtypes) {
        int maxSize = 500;
        if (mainPtypes.size() <= maxSize) {
            mapper.batchUpdateProductMappingXcode(profileId, mainPtypes);
        } else {
            for (int i = 0; i < mainPtypes.size(); i += maxSize) {
                int toIndex = Math.min(i + maxSize, mainPtypes.size() - 1);
                List<EshopProductSkuPageData> subList = mainPtypes.subList(i, toIndex);
                mapper.batchUpdateProductMappingXcode(profileId, subList);
            }
        }
    }

    private void batchUpdateProductSkuMappingXcode(List<EshopProductSkuPageData> updatedSkuData, ProductOperateLogType logType) {
        int maxSize = 500;
        BigInteger profileId = CurrentUser.getProfileId();
        if (updatedSkuData.size() <= maxSize) {
            mapper.batchUpdateProductSkuMappingXcode(profileId, updatedSkuData);
        } else {
            for (int i = 0; i < updatedSkuData.size(); i += maxSize) {
                int toIndex = Math.min(i + maxSize, updatedSkuData.size() - 1);
                List<EshopProductSkuPageData> subList = updatedSkuData.subList(i, toIndex);
                mapper.batchUpdateProductSkuMappingXcode(profileId, subList);
            }
        }
        for (EshopProductSkuPageData data : updatedSkuData) {
            EshopProductSkuMapping mapping = data.toSkuMapping();
            mapping.setReadySkuXcode(data.getOldPlatXCode());
            ProductManageUtil.doWriteLog(mapping, logType);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void doBindRelation(List<EshopProductSkuPageData> pageDataList) {
        try {
            List<EshopProductSkuMapping> mappingList = new ArrayList<>();
            List<EshopProductSkuExpand> expadList = new ArrayList<>();
            for (EshopProductSkuPageData pageData : pageDataList) {
                EshopProductSkuMapping mapping = pageData.toSkuMapping();
                EshopProductSkuExpand expand = pageData.toSkuExpand();
                expadList.add(expand);
                mappingList.add(mapping);
                ProductManageUtil.doWriteLog(mapping, ProductOperateLogType.BindMapping);
            }
            mapper.bindSkuMapping(mappingList);
            mapper.bindSkuMappingExpand(expadList);
        } catch (Exception ex) {
            logger.error("账套{}手工绑定对应关系报错:{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            throw new RuntimeException(String.format("手工绑定对应关系报错:%s", ex.getMessage()), ex);
        }
    }

    public List<EshopProductSkuAttrRelation> queryAttrRelationsByShop(BigInteger otypeId) {
        List<EshopProductSkuAttrRelation> relationList = mapper.querySkuAttrRelation(CurrentUser.getProfileId(), otypeId);
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        Map<String, EshopProductSkuAttrRelation> relationMap = relationList.stream().collect(Collectors.toMap(EshopProductSkuAttrRelation::getPlatformProp, relation -> relation));
        buildAttrRelationsByBaseProp(relationMap);
        ArrayList<EshopProductSkuAttrRelation> attrRelations = new ArrayList<>(relationMap.values());
        buildAttrRelationOperate(attrRelations);
        return attrRelations;
    }


    public void buildAttrRelationsMap(List<EshopProductSkuPageData> pageDataList, Map<String, EshopProductSkuAttrRelation> relationMap) {
        for (EshopProductSkuPageData sku : pageDataList) {
            if (StringUtils.isEmpty(sku.getPlatformFullProperties())) {
                continue;
            }
            String[] split = sku.getPlatformFullProperties().split(StringConstant.SEMICOLON);
            for (String item : split) {
                if (StringUtils.isEmpty(item)) {
                    continue;
                }
                if (!item.contains(StringConstant.COLON)) {
                    relationMap.putIfAbsent(item, new EshopProductSkuAttrRelation(item, sku.getOtypeId()));
                    continue;
                }
                String substring = item.substring(0, item.indexOf(StringConstant.COLON));
                boolean ignoreAttr = ProductManageUtil.checkIgnoreAttr(substring, config.getProductIgnoreAttr());
                if (!ignoreAttr) {
                    relationMap.put(substring, new EshopProductSkuAttrRelation(substring, sku.getOtypeId()));
                }
            }
        }
    }

    public List<EshopProductSkuAttrRelation> getAttrRelationList(List<EshopProductSkuPageData> pageDataList) {
        Map<String, EshopProductSkuAttrRelation> relationMap = new HashMap<>();
        buildAttrRelationsMap(pageDataList, relationMap);
        return buildAttrRelations(relationMap);
    }

    /**
     * 根据映射表找属性对应关系
     */
    public void buildAttrRelationsBySkuAttrRelation(Map<String, EshopProductSkuAttrRelation> relationMap) {
        List<EshopProductSkuAttrRelation> relationList = mapper.querySkuAttrRelation(CurrentUser.getProfileId(), null);
        relationList = relationList.stream().filter(relation -> relation.getPropId() != null && relation.getPropId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }
        //1.根据映射表找属性对应关系
        for (EshopProductSkuAttrRelation relation : relationList) {
            if (!relationMap.containsKey(relation.getPlatformProp())) {
                continue;
            }
            EshopProductSkuAttrRelation skuAttrRelation = relationMap.get(relation.getPlatformProp());
            //映射是店铺维护的。
            if (!Objects.equals(skuAttrRelation.getEshopId(), relation.getEshopId())) {
                continue;
            }
            relationMap.put(relation.getPlatformProp(), relation);
        }
    }

    /**
     * 根据本地属性找对应关系
     * 属性名称相同的自动映射
     */
    private void buildAttrRelationsByBaseProp(Map<String, EshopProductSkuAttrRelation> relationMap) {
        List<Prop> baseProps = baseInfoMapper.getEnablePropList(CurrentUser.getProfileId());
        if (CollectionUtils.isEmpty(baseProps)) {
            return;
        }
        List<EshopProductSkuAttrRelation> autoRelationList = new ArrayList<>();
        for (Prop baseProp : baseProps) {
            EshopProductSkuAttrRelation skuAttrRelation = relationMap.getOrDefault(baseProp.getPropName(), null);
            if (skuAttrRelation == null) {
                continue;
            }
            //已经有映射关系了
            if (skuAttrRelation.getPropId() != null && skuAttrRelation.getPropId().compareTo(BigInteger.ZERO) != 0) {
                continue;
            }
            skuAttrRelation.setPropId(baseProp.getId());
            skuAttrRelation.setLocalProp(baseProp.getPropName());
            skuAttrRelation.setRowIndex(baseProp.getRowindex());
            autoRelationList.add(skuAttrRelation);
        }
        if (CollectionUtils.isNotEmpty(autoRelationList)) {
            mapper.insertProductSkuAttrRelation(autoRelationList);
        }
    }

    public List<EshopProductSkuAttrRelation> buildAttrRelations(Map<String, EshopProductSkuAttrRelation> relationMap) {
        buildAttrRelationsBySkuAttrRelation(relationMap);
        buildAttrRelationsByBaseProp(relationMap);
        ArrayList<EshopProductSkuAttrRelation> attrRelations = new ArrayList<>(relationMap.values());
        buildAttrRelationOperate(attrRelations);
        return attrRelations;
    }


    private void buildAttrRelationOperate(List<EshopProductSkuAttrRelation> attrRelations) {
        for (EshopProductSkuAttrRelation attrRelation : attrRelations) {
            String add = "<font color='white' title='以线上属性添加到本地属性，并自动对应' style='background-color:#27BA6E;border-radius:3px;padding:4px;margin:4px'>增</font>";
            attrRelation.setOprate(add);
        }
    }

    public String batchModifyStockSyncState(EshopBatchModifyStockSyncRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getData())) {
                return "数据为空";
            }
            int result = mapper.batchUpdateStockSyncState(CurrentUser.getProfileId(), request.getIsOpen(), request.getData());
            if (result > 0) {
                String message = String.format("【%s】库存自动同步", request.getIsOpen() == 1 ? "开启" : "关闭");
                List<EshopProductMappingLog> logs=new ArrayList<>();
                for (EshopProductSkuPageData pageData : request.getData()) {
                    EshopProductMappingLog log = EshopProductMappingLogService.toMappingLog(pageData, request.getIsOpen() == 1
                            ? ProductOperateLogType.OPEN_SYNC_STOCK : ProductOperateLogType.CLOSE_SYNC_STOCK, message);
                    logs.add(log);
                }
                LogService.addRange(logs);
            }
            return result > 0 ? "" : "修改失败,可能是所选商品已被删除,请刷新页面后重试";
        } catch (Exception e) {
            logger.error("修改自动同步状态失败:{}", e.getMessage(), e);
            return e.getMessage();
        }
    }

    public List<Prop> queryLocalProps() {
        return baseInfoMapper.getEnablePropList(CurrentUser.getProfileId());
    }

    public List<MultiStockSyncDetail> queryMultiDetails(EshopQueryMultiDetailsRequest request) {
        return mapper.queryMultiDetails(CurrentUser.getProfileId(), request.getRuleId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public String saveDefaultRule(StockSyncRule rule, boolean needUpdate) {
        try {
            QueryStockRuleParameter parameter = new QueryStockRuleParameter();
            parameter.setEshopId(rule.getOtypeId());
            parameter.setProfileId(CurrentUser.getProfileId());
            StockSyncRule oldRule = ruleMapper.getDefaultRule(parameter);
            List<MultiStockSyncDetail> oldMultiDetails = mapper.queryMultiDetails(CurrentUser.getProfileId(), rule.getId());
            oldRule.setMultiStockSyncDetailList(oldMultiDetails);
            boolean needInsert = !needUpdate;
            if (needUpdate) {
                int count = mapper.updateDefaultRule(CurrentUser.getProfileId(), rule);
                needInsert = count == 0;
            }
            if (needInsert) {
                mapper.saveDefaultRule(CurrentUser.getProfileId(), rule);
            }
            updateMultiStockSyncDetails(rule);
            String ruleLog = buildRuleChangeLog(rule, oldRule, true);
            doLogRuleChange(StringConstantEnum.DEFAULT_RULE_LOG_TYPE, rule.getId(), ruleLog);
            return "";
        } catch (Exception ex) {
            logger.error("账套{},店铺:{},保存店铺默认规则出现异常:{}", CurrentUser.getProfileId(), rule.getOtypeId(), ex.getMessage(), ex);
            throw new RuntimeException(String.format("保存店铺默认规则失败:%s", ex.getMessage()), ex);
        }
    }

    private String buildRuleChangeLog(StockSyncRule newRule, StockSyncRule oldRule, boolean isDefault) {
        StringBuilder sb = new StringBuilder();
        boolean ruleNameChanged = !isDefault && !newRule.getRuleName().equals(oldRule.getRuleName());
        boolean isNewCustom = ruleNameChanged || StringUtils.isEmpty(oldRule.getRuleName());
        boolean openAutoEnable = newRule.isAutoSyncEnabled() && !oldRule.isAutoSyncEnabled();
        boolean closeAutoEnable = !newRule.isAutoSyncEnabled() && oldRule.isAutoSyncEnabled();
        boolean openZeroEnable = newRule.isZeroQtySyncEnabled() && !oldRule.isZeroQtySyncEnabled();
        boolean closeZeroEnable = !newRule.isZeroQtySyncEnabled() && oldRule.isZeroQtySyncEnabled();
        boolean cornChange = !newRule.getFormula().equals(oldRule.getFormula());
        boolean ktypeChanged = !newRule.getKtypeIds().equals(oldRule.getKtypeIds());
        boolean openWarehouseStockSync = ProductManageUtil.ifNull(newRule.getWarehouseStockSyncEnabled(), false)
                && !ProductManageUtil.ifNull(oldRule.getWarehouseStockSyncEnabled(), false);
        boolean closeWarehouseStockSync = !ProductManageUtil.ifNull(newRule.getWarehouseStockSyncEnabled(), false)
                && ProductManageUtil.ifNull(oldRule.getWarehouseStockSyncEnabled(), false);
        boolean openEshopMultiStockSync = ProductManageUtil.ifNull(newRule.getEshopMultiStockSyncEnabled(), false)
                && !ProductManageUtil.ifNull(oldRule.getEshopMultiStockSyncEnabled(), false);
        boolean closeEshopMultiStockSync = !ProductManageUtil.ifNull(newRule.getEshopMultiStockSyncEnabled(), false)
                && ProductManageUtil.ifNull(oldRule.getEshopMultiStockSyncEnabled(), false);

        String cornChangeMsg = String.format("【修改同步公式】原公式【%s】新公式【%s】", oldRule.getFormula(), newRule.getFormula());
        if (isNewCustom) {
            cornChangeMsg = String.format("【新增同步公式】【%s】", newRule.getFormula());
            doAppendMsg(sb, ruleNameChanged, String.format("【新增规则】【%s】", newRule.getRuleName()));
        } else {
            doAppendMsg(sb, ruleNameChanged, String.format("【修改规则名称】原名称【%s】新名称【%s】", oldRule.getRuleName(), newRule.getRuleName()));
        }
        doAppendMsg(sb, openZeroEnable, "【开启】0库存自动同步");
        doAppendMsg(sb, closeZeroEnable, "【关闭】0库存自动同步");
        doAppendMsg(sb, openAutoEnable, "【开启】库存自动同步");
        doAppendMsg(sb, closeAutoEnable, "【关闭】库存自动同步");
        doAppendMsg(sb, openWarehouseStockSync, "【开启】分仓同步库存");
        doAppendMsg(sb, closeWarehouseStockSync, "【关闭】分仓同步库存");
        doAppendMsg(sb, openEshopMultiStockSync, "【开启】时效同步库存");
        doAppendMsg(sb, closeEshopMultiStockSync, "【关闭】时效同步库存");
        doAppendMsg(sb, cornChange, cornChangeMsg);
        doAppendMsg(sb, ktypeChanged, getKtypeChangeMsg(ktypeChanged, newRule, oldRule, isNewCustom));


        return sb.toString();
    }


    private String getKtypeChangeMsg(boolean ktypeChanged, StockSyncRule newRule, StockSyncRule oldRule, boolean isNewCustom) {
        if (!ktypeChanged) {
            return "";
        }
        List<Stock> source = newRule.getKtypeDataSource();
        String newName = getKtypeNameStringByIds(newRule.getKtypeIds(), source);
        String oldName = getKtypeNameStringByIds(oldRule.getKtypeIds(), source);
        if (isNewCustom) {
            return String.format("【新增同步仓库】【%s】", newName);
        }
        return String.format("【修改同步仓库】原仓库【%s】新仓库【%s】", oldName, newName);
    }

    private String getKtypeNameStringByIds(String ktypeIds, List<Stock> ktypeSource) {
        if (StringUtils.isEmpty(ktypeIds)) {
            return "";
        }
        String[] split = ktypeIds.split(StringConstantEnum.COMMA.getSymbol());
        if (split.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (String k : split) {
            BigInteger ktypeId = new BigInteger(k);
            Optional<Stock> first = ktypeSource.stream().filter(x -> x.getId().equals(ktypeId)).findFirst();
            if (!first.isPresent()) {
                continue;
            }
            sb.append(first.get().getFullname()).append(",");
        }
        String ktypeName = sb.toString();
        if (StringUtils.isWhitespace(ktypeName)) {
            return "";
        }
        return ktypeName.substring(0, ktypeName.length() - 1);
    }


    private void doAppendMsg(StringBuilder sb, boolean condition, String msg) {
        if (!condition) {
            return;
        }
        sb.append(msg);
        if (msg != null && !"".equals(msg)) {
            sb.append("\n");
        }
    }

    private void updateMultiStockSyncDetails(StockSyncRule rule) {
        mapper.deleteMultiDetailsByRuleId(CurrentUser.getProfileId(), rule.getId());
        if (CollectionUtils.isEmpty(rule.getMultiStockSyncDetailList())) {
            return;
        }
        rule.getMultiStockSyncDetailList().forEach(detail -> {
            if (detail.getId() == null || detail.getId().longValue() == 0L) {
                detail.setId(UId.newId());
            }
            detail.setRuleId(rule.getId());
        });
        mapper.batchInsertMultiStockSyncDetails(CurrentUser.getProfileId(), rule.getMultiStockSyncDetailList());
    }

    public List<StockSyncRule> queryCustomRules(QueryStockRuleParameter parameter) {
        parameter.setProfileId(CurrentUser.getProfileId());
        List<StockSyncRule> syncRules = ruleMapper.queryCustomRules(parameter);
        if (CollectionUtils.isEmpty(syncRules)) {
            return syncRules;
        }
        List<BigInteger> ruleIds = syncRules.stream().map(StockSyncRule::getId).collect(Collectors.toList());
        QueryStockRuleParameter detailParameter = new QueryStockRuleParameter();
        detailParameter.setRuleIds(ruleIds);
        List<StockRuleDetail> detailList = ruleMapper.getRuleDetailList(detailParameter);
        if (CollectionUtils.isEmpty(detailList)) {
            return syncRules;
        }
        Map<BigInteger, List<StockRuleDetail>> listMap = detailList.stream().collect(Collectors.groupingBy(StockRuleDetail::getRuleId));
        for (StockSyncRule item : syncRules) {
            BigInteger ruleId = item.getId();
            if (!listMap.containsKey(ruleId)) {
                continue;
            }
            List<StockRuleDetail> details = listMap.get(ruleId);
            if (details.size() == 0) {
                continue;
            }
            List<BigInteger> ktypeList = details.stream().map(StockRuleDetail::getKtypeId).collect(Collectors.toList());
            item.setStockRuleDetailList(details);
            item.setKtypeIds(StringUtils.join(ktypeList, ","));
        }
        return syncRules;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public String saveCustomRule(StockSyncRule rule) {
        try {
            rule.setProfileId(CurrentUser.getProfileId());
            rule.setState(1);
            boolean isNew = rule.getId() == null || rule.getId().longValue() == 0L;
            StockSyncRule oldRule = null;
            if (isNew) {
                rule.setId(UId.newId());
            } else {
                QueryStockRuleParameter parameter = new QueryStockRuleParameter();
                parameter.setProfileId(CurrentUser.getProfileId());
                parameter.setRuleId(rule.getId());
                parameter.setRuleIds(Collections.singletonList(rule.getId()));
                List<StockSyncRule> oldRules = ruleMapper.queryCustomRules(parameter);
                if (CollectionUtils.isNotEmpty(oldRules)) {
                    oldRule = oldRules.get(0);
                    List<StockRuleDetail> ruleDetails = ruleMapper.getSyncRuleDetailByRuleIds(parameter);
                    if (CollectionUtils.isNotEmpty(ruleDetails)) {
                        oldRule.setStockRuleDetailList(ruleDetails);
                        oldRule.setKtypeIds(rule.getStockRuleDetailList().stream()
                                .filter(detail -> detail.getKtypeId() != null && detail.getKtypeId().longValue() > 0)
                                .map(detail -> detail.getKtypeId().toString()).distinct()
                                .collect(Collectors.joining(",")));
                    }
                }
            }

            if (oldRule == null) {
                oldRule = new StockSyncRule();
            }
            List<StockRuleDetail> ruleDetailList = buildDetailsByKtypes(rule);
            rule.setStockRuleDetailList(ruleDetailList);
            if (!isNew) {
                ruleMapper.deleteCustomRuleById(CurrentUser.getProfileId(), rule.getId());
                ruleMapper.deleteCustomRuleDetailByRuleId(CurrentUser.getProfileId(), rule.getId());
            }
            ruleMapper.insertCustomRule(rule);
            ruleMapper.batchInsertCustomRuleDetail(ruleDetailList);
            updateMultiStockSyncDetails(rule);
            String ruleLog = buildRuleChangeLog(rule, oldRule, false);
            doLogRuleChange(StringConstantEnum.RULE_LOG_TYPE, rule.getId(), ruleLog);
        } catch (Exception ex) {
            logger.error("账套{},保存自定义库存同步规则出现异常:{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage(), ex);
        }
        return "";
    }

    private List<StockRuleDetail> buildDetailsByKtypes(StockSyncRule rule) {
        if (StringUtils.isEmpty(rule.getKtypeIds())) {
            throw new RuntimeException("规则至少绑定一个仓库");
        }

        List<BigInteger> ktypeIds = Arrays.stream(rule.getKtypeIds().split(",")).filter(StringUtils::isNotEmpty)
                .map(BigInteger::new).collect(Collectors.toList());
        List<StockRuleDetail> stockRuleDetailList = new ArrayList<>();
        for (BigInteger ktypeId : ktypeIds) {
            StockRuleDetail detail = new StockRuleDetail();
            detail.setId(UId.newId());
            detail.setRuleId(rule.getId());
            detail.setKtypeId(ktypeId);
            detail.setProfileId(CurrentUser.getProfileId());
            detail.setTargetType(StockRuleTargetTypeEnum.ALL);
            stockRuleDetailList.add(detail);
        }
        return stockRuleDetailList;
    }

    public void saveEshopAttrRelation(List<EshopProductSkuAttrRelation> attrRelationList) {
        if (CollectionUtils.isEmpty(attrRelationList)) {
            return;
        }
        List<Prop> propList = baseInfoMapper.getEnablePropList(CurrentUser.getProfileId());
        if (propList == null || propList.isEmpty()) {
            return;
        }
        Map<BigInteger, Prop> localPropMap = propList.stream().collect(Collectors.toMap(Prop::getId, prop -> prop));
        for (EshopProductSkuAttrRelation relation : attrRelationList) {
            if (relation.getPropId() == null || relation.getPropId().compareTo(BigInteger.ZERO) == 0 || !localPropMap.containsKey(relation.getPropId())) {
                relation.setPropId(BigInteger.ZERO);
                relation.setLocalProp("");
            } else {
                Prop prop = localPropMap.get(relation.getPropId());
                relation.setLocalProp(prop.getPropName());
            }
        }
        mapper.insertProductSkuAttrRelation(attrRelationList);
    }

    public void createLocalPtype(CreatePtypeParams params, ProcessLoggerImpl processLogger) {
        ThreadPool test = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_DOWNLOAD.getName());
        ProductManageUtil.appendProcessMsg(processLogger, "开始生成本地商品，请稍后...");
        test.executeAsync(invoker -> {
            try {
                createPtype(params, processLogger);
            } catch (Exception ex) {
                logger.error("下载并生成本地商品报错，错误信息", ex);
                ProductManageUtil.appendProcessMsg(processLogger, ex.getMessage());
            } finally {
                ProductManageUtil.processFinish(processLogger);
            }
        }, "生成本地商品");
    }

    public void createPtype(CreatePtypeParams params, ProcessLoggerImpl pLogger) {
        BigInteger eshopId = params.getEshopId();
        if (params.getKtypeId() != null && params.getKtypeId().compareTo(BigInteger.ZERO) > 0) {
            params.setKtypeName(StringUtils.isNotEmpty(params.getKtypeName()) ? "默认仓库" : params.getKtypeName());
        }
        boolean isPropEnabled = params.isCreatePropPtype();
        BigInteger profileId = CurrentUser.getProfileId();
        List<EshopProductSkuMapping> eshopProductSkuMappings;
        if (CollectionUtils.isNotEmpty(params.getSkus())) {
            //批量新增本地商品
            List<String> uniqueIds = params.getSkus().stream().map(EshopProductSkuPageData::getUniqueId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            eshopProductSkuMappings = mapper.querySkusByUniqueIds(profileId, uniqueIds);
        } else {
            //按店铺新增本地商品
            eshopProductSkuMappings = mapper.querySkusByEshopId(profileId, eshopId);
        }
        ProductManageUtil.appendProcessMsg(pLogger, "正在构建系统商品信息，请稍后...");
        List<PtypeInfoExt> successPtypeInfos = new ArrayList<>();
        int skuTotalCount = eshopProductSkuMappings.size();
        int successSkuCount = 0;
        try {
            if (CollectionUtils.isEmpty(eshopProductSkuMappings)) {
                return;
            }
            //构建本地商品信息
            List<PtypeInfoExt> ptypeInfoExtList = ptypeSaver.buildPtypeInfoDtoList(isPropEnabled, eshopProductSkuMappings, params, pLogger);
            ProductManageUtil.appendProcessMsg(pLogger, "正在保存系统商品信息，请稍后...");
            ptypeSaver.savePtypeInfos(ptypeInfoExtList, pLogger);
            //基本信息组会把保存商品错误信息写到PtypeInfoDto中、回填ptypeId/skuId
            for (PtypeInfoExt ptypeInfoExt : ptypeInfoExtList) {
                int skuCount = CollectionUtils.isEmpty(ptypeInfoExt.getPtypeInfoDto().getSkus()) ? 1 : ptypeInfoExt.getPtypeInfoDto().getSkus().size();
                if (ptypeInfoExt.getPtypeInfoDto().getErrorObj() == null) {
                    successPtypeInfos.add(ptypeInfoExt);
                    successSkuCount += skuCount;
                }
            }
            if (CollectionUtils.isNotEmpty(successPtypeInfos)) {
                ptypeSaver.afterProcess(eshopProductSkuMappings, successPtypeInfos, ProductOperateLogType.DownloadProduct);
                //创建期初库存
                ptypeSaver.createInitStock(params, successPtypeInfos, pLogger);
            }
        } catch (Exception ex) {
            logger.error("账套ID：{}保存本地商品报错，错误信息：{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            ProductManageUtil.appendProcessMsg(pLogger, "生成系统商品失败，失败原因：" + ex.getMessage());
        } finally {
            RedisMessageUtil.set(String.format("%s%s",
                            StringConstant.PROCESS_SUCCESS_COUNT_KEY, pLogger.getLoggerKey()),
                    String.valueOf(successSkuCount), 60000);
            RedisMessageUtil.set(String.format("%s%s",
                            StringConstant.PROCESS_FAIL_COUNT_KEY, pLogger.getLoggerKey()),
                    String.valueOf(skuTotalCount - successSkuCount), 60000);
            ProductManageUtil.appendProcessMsg(pLogger, "生成系统商品结束");
        }
    }

    public String updateAutoSyncStockEnabled(StockSyncRule rule) {
        if (rule.isAutoSyncEnabled()) {
            boolean autoSyncUnSupported = eshopService.ShopTypesSupport(rule.getEshopType(), serviceConfig.getUnSupportAutoSyncStockShopTypes());
            if (autoSyncUnSupported) {
                throw new RuntimeException(String.format("%s 不支持开启自动同步库存！", rule.getEshopType().getName()));
            }
        }
        rule.setProfileId(CurrentUser.getProfileId());
        int count = ruleMapper.updateAutoSyncStockEnabled(rule);
        String result = "";
        if (count <= 0) {
            result = saveDefaultRule(rule, false);
        } else {
            doLogRuleChange(StringConstantEnum.DEFAULT_RULE_LOG_TYPE, rule.getId(),
                    (rule.isAutoSyncEnabled() ? "【开启】" : "【关闭】") + "库存自动同步");
        }
        return result;
    }

    public String bindRuleId(EshopProductBindRuleIdRequest request) {
        if (CollectionUtils.isEmpty(request.getData())) {
            return "没有需要设置库存规则的商品";
        }
        List<String> uniqueIds = request.getData().stream()
                .filter(data -> data.getPtypeId() != null
                        && data.getPtypeId().longValue() > 0
                        && StringUtils.isNotEmpty(data.getUniqueId()))
                .map(EshopProductSkuPageData::getUniqueId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return "没有需要设置库存规则的商品,请绑定对应关系后重试";
        }
        String result = mapper.bindRuleId(CurrentUser.getProfileId(), request.getRuleId(), uniqueIds) > 0 ? "" : "库存规则设置失败";

        if (StringUtils.isEmpty(result)) {
            String ruleName = StringUtils.isNotEmpty(request.getRuleName())
                    ? request.getRuleName()
                    : (request.getRuleId().longValue() == 0L ? "网店默认库存同步规则" : "自定义库存同步规则");
            List<EshopProductMappingLog> logs = new ArrayList<>();
            for (EshopProductSkuPageData pageData : request.getData()) {
                EshopProductMappingLog log = EshopProductMappingLogService.toMappingLog(pageData,
                        ProductOperateLogType.BIND_STOCK_RULE, String.format("绑定库存同步规则【%s】",
                                ruleName));
                logs.add(log);
            }
            LogService.addRange(logs);
        }
        return result;
    }

    public ComboSaveResponse createCombo(CreateComboParams params) {
        ComboSaveResponse response = new ComboSaveResponse();
        try {
            PtypeCombo ptypeCombo = ptypeSaver.getRepeatCombo(params);
            if (ptypeCombo != null) {
                response.setData(ptypeCombo);
                response.setCode(StringConstant.REPEAT_CODE);
                return response;
            }
            PtypeCombo saveCombo = ptypeSaver.saveCombo(params);
            response.setData(saveCombo);
            return response;
        } catch (Exception ex) {
            logger.error("账套{}快速创建套餐失败了，错误原因：{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            response.setMessage(String.format("快速生成套餐失败：%s！ 本次绑定失败，请确认后重试！", ex.getMessage()));
            response.setCode(StringConstant.ERROR_CODE);
            return response;
        }
    }

    public String batchDeleteCustomRule(EshopBatchDeleteCustomRuleRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getRuleIdList())) {
            return "请传入需要删除的规则列表";
        }
        ruleMapper.batchDeleteCustomRule(CurrentUser.getProfileId(), request.getRuleIdList());
        return "";
    }

    public void buildMarkByPageData(List<EshopProductSkuPageData> pageDataList, boolean isExport) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return;
        }
        List<String> list = pageDataList.stream().map(EshopProductSkuPageData::getUniqueId).collect(Collectors.toList());
        QuerySkuMappingRequest request = new QuerySkuMappingRequest();
        request.setUniqueIdList(list);
        List<EshopProductMark> productMarks = queryMarkList(request);
        if (CollectionUtils.isEmpty(productMarks)) {
            clearMarkList(pageDataList);
            return;
        }
        Map<String, List<EshopProductMark>> listMap = productMarks.stream().collect(Collectors.groupingBy(EshopProductMark::getUniqueId));
        for (EshopProductSkuPageData pageData : pageDataList) {
            String uniqueId = pageData.getUniqueId();
            List<EshopProductMark> markList = listMap.get(uniqueId);
            if (CollectionUtils.isEmpty(markList)) {
                clearMark(pageData);
                continue;
            }
            doBuildMarkRow(pageData, markList, isExport);
        }
    }

    private void clearMarkList(List<EshopProductSkuPageData> pageDataList) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return;
        }
        for (EshopProductSkuPageData pageData : pageDataList) {
            clearMark(pageData);
        }
    }

    private void clearMark(EshopProductSkuPageData pageData) {
        pageData.setMarkList(new ArrayList<>());
        pageData.setMark("");
    }

    private List<EshopProductMark> queryMarkList(QuerySkuMappingRequest request) {
        if (CollectionUtils.isEmpty(request.getUniqueIdList())) {
            throw new RuntimeException("查询条件不能为空");
        }
        return mapper.getProductMarkList(request);
    }

    private void doBuildMarkRow(EshopProductSkuPageData pageData,
                                List<EshopProductMark> markList,
                                boolean isExport) {
        if (CollectionUtils.isEmpty(markList)) {
            clearMark(pageData);
            return;
        }
        pageData.setMarkList(markList);
        StringBuilder sbBtn = new StringBuilder();
        sbBtn.append("[");
        StringBuilder exportMarks = new StringBuilder();
        List<Integer> displayCodes = config.getAllowShowInPtypeRelationMarksList();
        for (EshopProductMark productMark : markList) {
            EshopProductMarkEnum enumByCode = EshopProductMarkEnum.getEnumByCode(productMark.getMarkCode());
            if (enumByCode == null) {
                continue;
            }
            if (enumByCode.getMarkShowType().equals(MarkShowTypeEnum.HIDE)) {
                continue;
            }
            if (!displayCodes.contains(productMark.getMarkCode())) {
                continue;
            }
            if (isExport) {
                if (exportMarks.length() > 0) {
                    exportMarks.append(",");
                }
                exportMarks.append(enumByCode.getName());
            } else {
                String bubble = productMark.getBubble();
                if (enumByCode.getCode() == EshopProductMarkEnum.ADVANCE.getCode()) {
                    EshopProductMarkBigData bigData = productMark.getProductMarkBigDataEntity();
                    if (bigData != null) {
                        if (StringUtils.isNotEmpty(bigData.getSendAfterPaiedDays()) && !"0".equals(bigData.getSendAfterPaiedDays())) {
                            bubble = String.format("商家预售,最晚发货时间:付款后%s天内发货", bigData.getSendAfterPaiedDays());
                        } else if (StringUtils.isNotEmpty(bigData.getSendOnDate())) {
                            bubble = String.format("商家预售,最晚发货时间:%s", bigData.getSendOnDate());
                        }
                    }
                }
                HashMap<Integer, String> hp = new HashMap();
                hp.put(enumByCode.getCode(), String.format("<font title='%s' color=\'white\'  style=\' background-color:%s;border-radius:3px;padding:2px;margin-top:2px\'>%s</font>", bubble, enumByCode.getColor(), enumByCode.getName()));

                if (sbBtn.length() > 1) {
                    sbBtn.append(",");
                }
                sbBtn.append(JsonUtils.toJson(hp));
            }
        }
        if (isExport) {
            pageData.setMark(exportMarks.toString());
        } else {
            sbBtn.append("]");
            pageData.setMark(sbBtn.toString());
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public String saveProductMark(EshopUpdateProductMarkRequest request) {
        String msg = "";
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            return msg;
        }
        ProcessLoggerImpl pLogger = request.getProcessLogger();
        List<EshopProductSkuPageData> skuList = request.getSkuList();
        ProductManageUtil.appendProcessMsg(pLogger, "开始设置网店商品标记，请稍后");
        ProductManageUtil.appendProcessMsg(pLogger, String.format("正在处理网店商品标记,共%s条，正在处理第%s条,失败%s条",
                request.getSkuList().size(), 0, 0));
        List<EshopProductMark> productMarkList = new ArrayList<>();
        List<EshopProductMarkData> productMarkDataList = new ArrayList<>();
        //插入前删除
        try {
            //按配置构建每个商品的新标记
            StringBuilder logAfterStringBuilder = new StringBuilder();
            Map<Integer, List<EshopProductMark>> newMarkByCodeMap = buildNewMarkListFromConfig(request.getMarkConfig(), logAfterStringBuilder);
            //修改后的标记日志
            String logAfter = logAfterStringBuilder.length() == 0
                    ? NO_MARK_DESCRIPTION
                    : logAfterStringBuilder.toString();

            List<String> uniqueIdList = skuList.stream()
                    .map(EshopProductSkuPageData::getUniqueId)
                    .collect(Collectors.toList());
            //修改前的标记
            QuerySkuMappingRequest queryRequest = new QuerySkuMappingRequest();
            queryRequest.setUniqueIdList(uniqueIdList);
            List<EshopProductMark> oldMarkList = queryMarkList(queryRequest);
            Map<String, List<EshopProductMark>> oldMarkUniqueIdMap = CollectionUtils.isEmpty(oldMarkList)
                    ? new HashMap<>() :
                    oldMarkList.stream().collect(Collectors.groupingBy(EshopProductMark::getUniqueId));

            //构建
            buildNewProductMarksAndLogs(request, oldMarkUniqueIdMap,
                    newMarkByCodeMap,
                    productMarkList,
                    productMarkDataList,
                    logAfter);
            //数据库操作
            if (CollectionUtils.isNotEmpty(uniqueIdList)) {
                ProductManageUtil.appendProcessMsg(pLogger, "正在清理老标记");
                markMapper.cleanProductMarkByUniqueId(CurrentUser.getProfileId(),
                        uniqueIdList,
                        Arrays.asList(config.getDeletedProductMarkCodeList().split(",")));
            }
            ProductManageUtil.appendProcessMsg(pLogger, "正在保存标记信息");
            if (CollectionUtils.isNotEmpty(productMarkList)) {
                markMapper.bulkInsertProductMark(productMarkList);
            }
            if (CollectionUtils.isNotEmpty(productMarkDataList)) {
                markBigDataMapper.bulkInsertProductMarkData(productMarkDataList);
            }
            //异步更新订单
            asyncUpdateOrders(request);
            ProductManageUtil.modifyProcessPercent(pLogger, "redis_process_success_count_", skuList.size(), 60000);
        } catch (Exception e) {
            ProductManageUtil.appendProcessMsg(pLogger, "设置网店商品标记出错，错误原因:" + e.getMessage());
            logger.error("设置网店商品标记出错,{}", e.getMessage(), e);
            ProductManageUtil.modifyProcessPercent(pLogger, "redis_process_failed_count_", skuList.size(), 60000);
            throw new RuntimeException(e);
        } finally {
            ProductManageUtil.modifyProcessPercent(pLogger, EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE, 100, 60000);
            ProductManageUtil.appendProcessMsg(pLogger, "设置网店商品标记完成");
            ProductManageUtil.processFinish(pLogger);
        }
        return msg;
    }

    private void asyncUpdateOrders(EshopUpdateProductMarkRequest request) {
        ThreadPool pool = ThreadPoolFactory.build(NameConstantEnum.OPERATE_ORDER_AFTER_PTYPE_SAVE.getName());
        pool.executeAsync(invoke -> {
            try {
                for (EshopProductSkuPageData sku : request.getSkuList()) {
                    downloadOrderMark(sku);
                }
            } catch (Exception ex) {
                logger.error("网店商品标记之后执行订单更新报错了，错误信息：{}", ex.getMessage(), ex);
            }
        }, null);
    }

    private void downloadOrderMark(EshopProductSkuPageData sku) {
        if (sku.getPtypeId() == null || sku.getPtypeId().longValue() == 0) {
            return;
        }
        if (StringUtils.isEmpty(sku.getPlatformNumId())) {
            return;
        }
        QueryOrderParameter queryOrderParameter = new QueryOrderParameter();
        queryOrderParameter.setProfileId(CurrentUser.getProfileId());
        queryOrderParameter.setOtypeIds(Collections.singletonList(sku.getOtypeId()));
        queryOrderParameter.setPlatformPtypeId(sku.getPlatformNumId());
        queryOrderParameter.setPlatformPropertiesName(sku.getPlatformPropertiesName() == null
                ? ""
                : sku.getPlatformPropertiesName());
        queryOrderParameter.setProcessState(ProcessState.NoSubmit);
        queryOrderParameter.setMappingState(MappingState.NotMapping);
        queryOrderParameter.setTimeType(QueryOrderTimeType.TRADE_CREATE_TIME);
        queryOrderParameter.setEndTime(DateUtils.getDate());
        queryOrderParameter.setBeginTime(DateUtils.addDays(DateUtils.getDate(), config.getProductMarkNotifyOrderDays() * -1));
        List<String> tradeOrderIds = orderService.getSaleOrderTradeIds(queryOrderParameter);
        if (CollectionUtils.isEmpty(tradeOrderIds)) {
            return;
        }
        List<List<String>> splitList = com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil.splitList(tradeOrderIds, 500);
        EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
        task.setOtypeId(sku.getOtypeId());
        task.setStatus(TradeStatus.All);
        task.setFilterType(OrderDownloadFilterType.NONE);
        task.setDownloadType(DownloadType.BY_TRADE_ID);
        task.setTradeFrom(StringConstant.TRADE_FROM_MANUAL_RELATION);
        for (List<String> tradeIds : splitList) {
            task.setFilterStr(String.join(",", tradeIds));
            orderService.downloadOrderById(task);
        }
    }

    private void buildNewProductMarksAndLogs(
            EshopUpdateProductMarkRequest request,
            Map<String, List<EshopProductMark>> oldMarkUniqueIdMap,
            Map<Integer, List<EshopProductMark>> newMarkByCodeMap,
            List<EshopProductMark> productMarkList,
            List<EshopProductMarkData> productMarkDataList,
            String logAfter) {

        //默认或者选择覆盖都是覆盖
        boolean isCover = null == request.getMarkConfig().getModifyType() || 1 == request.getMarkConfig().getModifyType();
        List<Integer> markCodes = config.getAllowShowInPtypeRelationMarksList();
        for (int i = 0; i < request.getSkuList().size(); i++) {
            EshopProductSkuPageData sku = request.getSkuList().get(i);
            StringBuilder newStringBuilder = new StringBuilder();
            StringBuilder oldStringBuilder = new StringBuilder();
            List<EshopProductMark> oldMarks = oldMarkUniqueIdMap.get(sku.getUniqueId());
            //新老标记都为空,跳过
            if (CollectionUtils.isEmpty(oldMarks) && CollectionUtils.isEmpty(newMarkByCodeMap)) {
                continue;
            }
            Map<Integer, List<EshopProductMark>> oldMarkMap = CollectionUtils.isEmpty(oldMarks)
                    ? new HashMap<>()
                    : oldMarks.stream().collect(Collectors.groupingBy(EshopProductMark::getMarkCode));
            boolean isNew = CollectionUtils.isEmpty(oldMarks) ||
                    oldMarks.stream().noneMatch(mark -> markCodes.contains(mark.getMarkCode()));
            if (isNew) {
                newStringBuilder.append("新增标记:");
            } else {
                oldStringBuilder.append("修改前标记:");
                newStringBuilder.append("修改后标记:");
            }
            //是否存在老标记追加
            boolean existOldAdd = false;
            for (Integer markCode : markCodes) {
                if (!newMarkByCodeMap.containsKey(markCode) && !oldMarkMap.containsKey(markCode)) {
                    continue;
                }
                EshopProductMark realMark = initialMark(sku);
                realMark.setMarkCode(markCode);
                EshopProductMarkEnum markEnum = EshopProductMarkEnum.getEnumByCode(markCode);

                boolean needAdd = false;
                if (CollectionUtils.isNotEmpty(oldMarkMap.get(markCode))) {
                    EshopProductMark oldMark =
                            oldMarkMap.get(markCode).get(0);

                    //追加场景,新标记不包含,且追加的标记不能与已有的标记冲突
                    if (!isCover
                            && !newMarkByCodeMap.containsKey(markCode)
                            && isNotConcurrentMark(markCode, newMarkByCodeMap.keySet())) {
                        needAdd = true;
                        existOldAdd = true;
                        realMark.setBubble(oldMark.getBubble());
                        realMark.setShowType(oldMark.getShowType());
                        realMark.setProductMarkBigData(oldMark.getProductMarkBigData());
                        if (markEnum != null) {
                            newStringBuilder.append(markEnum.getName()).append(",");
                        }
                    }

                    if (markEnum != null) {
                        oldStringBuilder.append(markEnum.getName()).append(",");
                    }
                }

                if (CollectionUtils.isNotEmpty(newMarkByCodeMap.get(markCode))) {
                    needAdd = true;
                    EshopProductMark newMark = newMarkByCodeMap.get(markCode).get(0);
                    realMark.setBubble(newMark.getBubble());
                    realMark.setShowType(newMark.getShowType());
                    realMark.setProductMarkBigData(newMark.getProductMarkBigData());
                }

                if (needAdd) {
                    productMarkList.add(realMark);
                    if (StringUtils.isNotEmpty(realMark.getProductMarkBigData())) {
                        EshopProductMarkData newMarkData = new EshopProductMarkData();
                        newMarkData.setId(UId.newId());
                        newMarkData.setMarkId(realMark.getId());
                        newMarkData.setBigData(realMark.getProductMarkBigData());
                        newMarkData.setProfileId(CurrentUser.getProfileId());
                        newMarkData.setEtypeId(CurrentUser.getEmployeeId());
                        productMarkDataList.add(newMarkData);
                    }
                }
            }
            //如果存在老标记追加,且新标记为空,则不处理
            if (!existOldAdd || !NO_MARK_DESCRIPTION.equals(logAfter)) {
                newStringBuilder.append(logAfter);
            }
            ProcessLoggerImpl processLogger = request.getProcessLogger();
            buildMarkEditLog(sku, oldStringBuilder, newStringBuilder, isNew);
            ProductManageUtil.modifyCurrentProcessMsg(processLogger, String.format("正在处理网店商品标记,共%s条，正在处理第%s条,失败%s条",
                    request.getSkuList().size(), i + 1, 0));
        }
    }

    private void buildMarkEditLog(EshopProductSkuPageData sku,
                                  StringBuilder oldStringBuilder,
                                  StringBuilder newStringBuilder,
                                  boolean isNew) {
        String logMsg;
        ProductOperateLogType logType;
        if (isNew) {
            logType = ProductOperateLogType.ADD_MARK;
            logMsg = newStringBuilder.substring(0, newStringBuilder.length() - 1);
        } else if (newStringBuilder.indexOf(NO_MARK_DESCRIPTION) > -1) {
            logType = ProductOperateLogType.CLEAN_MARK;
            logMsg = oldStringBuilder.substring(0, oldStringBuilder.length() - 1) + ";" + newStringBuilder;
        } else {
            logType = ProductOperateLogType.MODIFY_MARK;
            logMsg = oldStringBuilder.substring(0, oldStringBuilder.length() - 1) + ";"
                    + newStringBuilder.substring(0, newStringBuilder.length() - 1);
        }
        EshopProductMappingLog log = EshopProductMappingLogService.toMappingLog(sku, logType, logMsg);
        LogService.add(log);
    }

    private boolean isNotConcurrentMark(Integer markCode, Set<Integer> newMarkCodeSet) {
        if (markCode == EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getCode()) {
            return !newMarkCodeSet.contains(EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getCode());
        } else if (markCode == EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getCode()) {
            return !newMarkCodeSet.contains(EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getCode());
        } else if (markCode == EshopProductMarkEnum.OCCUPYSTOCKQTY.getCode()) {
            return !newMarkCodeSet.contains(EshopProductMarkEnum.NOTOCCUPYSTOCKQTY.getCode());
        } else if (markCode == EshopProductMarkEnum.NOTOCCUPYSTOCKQTY.getCode()) {
            return !newMarkCodeSet.contains(EshopProductMarkEnum.OCCUPYSTOCKQTY.getCode());
        }
        return true;
    }

    private EshopProductMark initialMark(EshopProductSkuPageData sku) {
        EshopProductMark mark = new EshopProductMark();
        mark.setId(UId.newId());
        mark.setProfileId(CurrentUser.getProfileId());
        mark.setEshopId(sku.getOtypeId());
        mark.setPlatformNumId(sku.getPlatformNumId());
        mark.setPlatformSkuId(sku.getPlatformSkuId());
        mark.setPlatformPropertiesName(sku.getPlatformPropertiesName());
        mark.setUniqueId(sku.getUniqueId());
        mark.setPreSendByDays(BigInteger.ZERO);
        mark.setFrequencyBlankNum(BigInteger.ZERO);
        mark.setFrequencyAssignCycle(BigInteger.ZERO);
        mark.setFrequencyBlankCycle(BigInteger.ZERO);
        mark.setFrequencyAssignCount(BigInteger.ZERO);
        mark.setFrequencyBlankCount(BigInteger.ZERO);
        mark.setFrequencyAssignDays("");
        mark.setFrequencyPhase(BigInteger.ZERO);
        mark.setMemo("");
        return mark;
    }

    private Map<Integer, List<EshopProductMark>> buildNewMarkListFromConfig(EshopProductMarkConfig markConfig, StringBuilder markLogAfter) {
        List<EshopProductMark> productMarkList = new ArrayList<>();
        if (markConfig == null) {
            return new HashMap<>();
        }
        //商品预售
        if (markConfig.getAdvance() != null && markConfig.getAdvance()) {
            EshopProductMark mark = new EshopProductMark();
            mark.setMarkCode(EshopProductMarkEnum.ADVANCE.getCode());
            mark.setBubble(EshopProductMarkEnum.ADVANCE.getBubble());
            mark.setShowType(EshopProductMarkEnum.ADVANCE.getMarkShowType().getCode());
            EshopProductMarkBigData bigData = new EshopProductMarkBigData();
            bigData.setProfileId(CurrentUser.getProfileId());
            bigData.setEtypeId(CurrentUser.getEmployeeId());
            int sendType = markConfig.getSendType() == null ? 0 : markConfig.getSendType();
            if (0 == sendType || 1 == sendType) {
                //定时发货
                if (1 == sendType) {
                    //yyyy-MM-dd
                    bigData.setSendOnDate(StringUtils.isEmpty(markConfig.getSendOnDate())
                            ? ""
                            : markConfig.getSendOnDate().length() > 10
                            ? markConfig.getSendOnDate().substring(0, 10)
                            : markConfig.getSendOnDate());
                    bigData.setDeliverPolicy("定时发货:" + bigData.getSendOnDate());
                } else {
                    //付款后N天内发货
                    bigData.setSendAfterPaiedDays(markConfig.getSendAfterPaiedDays() == null ?
                            "" :
                            markConfig.getSendAfterPaiedDays());
                    bigData.setDeliverPolicy("付款后" + bigData.getSendAfterPaiedDays() + "天内发货");
                }
                mark.setProductMarkBigData(JsonUtils.toJson(bigData));
                productMarkList.add(mark);
                markLogAfter.append(EshopProductMarkEnum.ADVANCE.getName()).append(",");
            }
        }

        //发货记账配置
        if (markConfig.getSendOrKeepAccounts() != null) {
            EshopProductMark mark = new EshopProductMark();
            if (markConfig.getSendOrKeepAccounts() == 0) {
                mark.setMarkCode(EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getCode());
                mark.setBubble(EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getBubble());
                mark.setShowType(EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getMarkShowType().getCode());
                productMarkList.add(mark);
                markLogAfter.append(EshopProductMarkEnum.NOTSEND_NOTKEEPACCOUNT.getName()).append(",");
            } else if (markConfig.getSendOrKeepAccounts() == 1) {
                mark.setMarkCode(EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getCode());
                mark.setBubble(EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getBubble());
                mark.setShowType(EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getMarkShowType().getCode());
                productMarkList.add(mark);
                markLogAfter.append(EshopProductMarkEnum.NOTSEND_KEEPACCOUNT.getName()).append(",");
            }
        }

        if (CollectionUtils.isNotEmpty(markConfig.getSerialType())) {
            for (String serialType : markConfig.getSerialType()) {
                if (StringUtils.isEmpty(serialType)) {
                    continue;
                }
                int code = Integer.parseInt(serialType);
                EshopProductMarkEnum markEnum = EshopProductMarkEnum.getEnumByCode(code);
                if (markEnum == null) {
                    continue;
                }
                EshopProductMark mark = new EshopProductMark();
                mark.setMarkCode(markEnum.getCode());
                mark.setBubble(markEnum.getBubble());
                mark.setShowType(markEnum.getMarkShowType().getCode());
                productMarkList.add(mark);
                markLogAfter.append(markEnum.getName()).append(",");
            }
        }
        return productMarkList.stream().collect(Collectors.groupingBy(EshopProductMark::getMarkCode));
    }


    public void batchUnbindManualRelation(List<EshopProductSkuPageData> pageDataList) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return;
        }
        List<String> uniqueIds = new ArrayList<>();
        List<EshopProductSkuMapping> needUpdateSkus = new ArrayList<>();
        for (EshopProductSkuPageData pageData : pageDataList) {
            uniqueIds.add(pageData.getUniqueId());
            needUpdateSkus.add(pageData.toSkuMapping());
        }
        //删除映射关系
        mapper.batchDeleteSkuMapping(CurrentUser.getProfileId(), uniqueIds);
        LogService.addRange(EshopProductMappingLogService.toMappingLogList(needUpdateSkus, ProductOperateLogType.MANUAL_MAPPING_UNBIND));
        //解除手工绑定后默认变回按商家编码对应
        mapper.updateSkuExpandMappingType(CurrentUser.getProfileId(), uniqueIds, MappingType.XCODEMAPPING.getCode());
        //按商家编码对应并保存对应关系
        Map<String, List<PtypeXcode>> listMap = productSaver.buildLocalXcodeMap(needUpdateSkus);
        if (CollectionUtils.isNotEmpty(listMap)) {
            List<EshopProductSkuMapping> needInsert = new ArrayList<>();
            for (EshopProductSkuMapping skuMapping : needUpdateSkus) {
                if (StringUtils.isBlank(skuMapping.getPlatformXcode())) {
                    continue;
                }
                productSaver.doBuildSkuMappingByXcode(skuMapping, listMap);
                if (skuMapping.isBind()) {
                    needInsert.add(skuMapping);
                    String log = EshopProductMappingLogService.getBindingLog("解除手工绑定后按商家编码自动对应,新的对应关系", skuMapping);
                    LogService.add(EshopProductMappingLogService.toMappingLog(skuMapping, ProductOperateLogType.MANUAL_MAPPING_UNBIND, log));
                }
            }
            mapper.batchInsertOrUpdateProductSkuMapping(needInsert);
        }
    }

    public void syncStock(List<EshopProductSkuPageData> skuPageDataList,
                          ProcessLoggerImpl processLogger) {
        int failedCount = 0;
        try {
            processLogger.appendMsg("开始同步网店商品库存...");
            if (CollectionUtils.isEmpty(skuPageDataList)) {
                processLogger.appendMsg("没有需要同步的网店商品库存！");
            }
        } catch (Exception ex) {
            processLogger.appendMsg("库存同步报错了：" + ex.getMessage());
        } finally {
            RedisMessageUtil.set(String.format("%s%s",
                            EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE,
                            processLogger.getLoggerKey()),
                    "100",
                    60000);
            RedisMessageUtil.set(String.format("%s%s", "redis_process_failed_count_",
                    processLogger.getLoggerKey()), String.valueOf(failedCount));

            RedisMessageUtil.set(String.format("%s%s", "redis_process_success_count_",
                            processLogger.getLoggerKey()),
                    String.valueOf(skuPageDataList.size() - failedCount), 60000);
            processLogger.appendMsg("网店商品库存同步完成！");
            processLogger.doFinish();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteSkus(List<EshopProductSkuPageData> skuInfos) {
        if (CollectionUtils.isEmpty(skuInfos)) {
            return;
        }
        Map<BigInteger, List<EshopProductSkuPageData>> skusByEshop = skuInfos.stream().collect(Collectors.groupingBy(EshopProductSkuPageData::getOtypeId));
        BigInteger profileId = CurrentUser.getProfileId();
        skusByEshop.forEach((eshopId, skuList) -> {
            List<List<EshopProductSkuPageData>> skuListPart = Lists.partition(skuList, config.getPageSize());
            for (List<EshopProductSkuPageData> skus : skuListPart) {
                List<String> uniqueIds = skus.stream().map(EshopProductSkuPageData::getUniqueId).distinct().collect(Collectors.toList());
                //删除sku表、删除sku扩展表、删除sku映射表、删除标记表数据
                mapper.deleteNotUseSku(profileId, eshopId, uniqueIds);
                LogService.addRange(EshopProductMappingLogService.toProductMappingLogs(skus, ProductOperateLogType.DELETE_SKU, "手工操作删除SKU"));
            }
            //删除没有SKU的主商品
            mapper.deleteProduct(profileId, eshopId);
        });
    }

    private void doLogRuleChange(StringConstantEnum type, BigInteger objectId, String body) {
        if (StringUtils.isEmpty(body)) {
            return;
        }
        BaseInfoLog infoLog = new BaseInfoLog();
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        String hostIp = IpUtils.getLocalHostIp();
        infoLog.setId(UId.newId());
        infoLog.setLogTime(new Date());
        infoLog.setProfileId(profileId);
        infoLog.setIp(hostIp);
        infoLog.setEtypeId(employeeId);
        infoLog.setEfullname(etypeUtil.getEtypeName());
        infoLog.setObjectId(objectId);
        infoLog.setObjectType(type.getSymbol());
        infoLog.setBody(body);
        LogService.add(infoLog);
    }

    public void syncProductRelationToSonShop(List<EshopInfo> eshopInfos) {
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.SYNC_PRODUCT_RELATION_AND_MARK.getName());
        threadPool.executeAsync(invoker -> doSyncProductRelationToSonShop(eshopInfos), eshopInfos);
    }

    /**
     * 同步商品标记到子店
     */
    private void doSyncProductMark(List<EshopInfo> eshopInfos, Map<BigInteger, EshopProcess> eshopProcessMap) {
        SaleBizConfig saleBizConfig = GetBeanUtil.getBean(SaleBizConfig.class);
        BigInteger mainOtypeId = eshopInfos.get(0).getMainOtypeId();
        int totalCount = mapper.queryEshopProductMarkCount(CurrentUser.getProfileId(), mainOtypeId);
        int pageCount = saleBizConfig.getPageCount(totalCount);
        for (int i = 0; i < pageCount; i++) {
            boolean isStart = i == 0;
            boolean isEnd = i == pageCount - 1;
            List<EshopProductMark> productMarks = mapper.queryEshopProductMarkByLimit(CurrentUser.getProfileId(), mainOtypeId, i * saleBizConfig.getPageSize(), saleBizConfig.getPageSize());
            for (EshopInfo eshopInfo : eshopInfos) {
                EshopProcess eshopProcess = eshopProcessMap.get(eshopInfo.getOtypeId());
                try {
                    executeSyncProductMark(eshopInfo, productMarks, eshopProcess, isStart, isEnd);
                } catch (Exception ex) {
                    logger.error("账套ID：{}，店铺ID：{},子店铺ID：{},同步网店商品标记到子店出错：{}", CurrentUser.getProfileId()
                            , eshopInfo.getMainOtypeId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
                    ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), String.format("同步商品标记失败，失败原因：%s", ex.getMessage()));
                }
            }
        }
    }

    /**
     * 同步商品对应关系到子店
     */
    private void doSyncProductRelation(List<EshopInfo> eshopInfos, Map<BigInteger, EshopProcess> eshopProcessMap) {
        SaleBizConfig saleBizConfig = GetBeanUtil.getBean(SaleBizConfig.class);
        BigInteger mainOtypeId = eshopInfos.get(0).getMainOtypeId();
        int totalCount = mapper.queryEshopSkuMappingCount(CurrentUser.getProfileId(), mainOtypeId);
        int pageCount = saleBizConfig.getPageCount(totalCount);
        for (int i = 0; i < pageCount; i++) {
            boolean isStart = i == 0;
            boolean isEnd = i == pageCount - 1;
            List<EshopProductSkuMapping> eshopProductSkuMappings = mapper.queryEshopSkuMappingByLimit(CurrentUser.getProfileId(), mainOtypeId, i * saleBizConfig.getPageSize(), saleBizConfig.getPageSize());
            for (EshopInfo eshopInfo : eshopInfos) {
                EshopProcess eshopProcess = eshopProcessMap.get(eshopInfo.getOtypeId());
                try {
                    executeSyncProductRelation(eshopInfo, eshopProductSkuMappings, eshopProcess, isStart, isEnd);
                } catch (Exception ex) {
                    logger.error("账套ID：{}，店铺ID：{},子店铺ID：{},同步网店商品对应关系到子店出错：{}", CurrentUser.getProfileId()
                            , eshopInfo.getMainOtypeId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
                    ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), String.format("同步商品对应关系失败，失败原因:%s", ex.getMessage()));
                }
            }
        }
    }

    private void doSyncProductRelationToSonShop(List<EshopInfo> eshopInfos) {
        if (CollectionUtils.isEmpty(eshopInfos)) {
            return;
        }
        BigInteger mainOtypeId = eshopInfos.get(0).getMainOtypeId();
        Map<BigInteger, EshopProcess> eshopProcessMap = new HashMap<>();
        try {
            syncToSonShopProcess(eshopInfos, eshopProcessMap, ProcessMessageType.INIT);
            doSyncProductRelation(eshopInfos, eshopProcessMap);
            doSyncProductMark(eshopInfos, eshopProcessMap);
            syncToSonShopProcess(eshopInfos, eshopProcessMap, ProcessMessageType.SYNC_COMPLETED);
        } catch (Exception ex) {
            logger.error("账套ID：{}，店铺ID：{},同步网店商品打标/对应关系到子店出错：{}", CurrentUser.getProfileId()
                    , mainOtypeId, ex.getMessage(), ex);
            syncToSonShopProcess(eshopInfos, eshopProcessMap, ProcessMessageType.SYNC_FAIL, String.format("同步失败，失败原因:%s", ex.getMessage()));
        } finally {
            syncToSonShopProcess(eshopInfos, eshopProcessMap, ProcessMessageType.FINISH);
        }
    }

    private void syncToSonShopProcess(List<EshopInfo> eshopInfos, Map<BigInteger, EshopProcess> eshopProcessMap, ProcessMessageType msgType) {
        syncToSonShopProcess(eshopInfos, eshopProcessMap, msgType, "");
    }

    private void syncToSonShopProcess(List<EshopInfo> eshopInfos, Map<BigInteger, EshopProcess> eshopProcessMap, ProcessMessageType msgType, String msg) {
        for (EshopInfo eshopInfo : eshopInfos) {
            if (msgType == ProcessMessageType.INIT) {
                ProcessLoggerImpl processLogger = new ProcessLoggerImpl(eshopInfo.getOtypeId().toString());
                EshopProcess eshopProcess = new EshopProcess();
                eshopProcess.setProcessLogger(processLogger);
                eshopProcess.setOtypeId(eshopInfo.getOtypeId());
                eshopProcessMap.put(eshopInfo.getOtypeId(), eshopProcess);
                ProductManageUtil.appendProcessMsg(processLogger, "开始同步");
            } else if (msgType == ProcessMessageType.SYNC_COMPLETED) {
                EshopProcess eshopProcess = eshopProcessMap.get(eshopInfo.getOtypeId());
                msg = "同步成功";
                if (!eshopProcess.isSyncProductRelationSuccess() && !eshopProcess.isSyncProductMarkSuccess()) {
                    msg = "同步失败";
                } else if (!eshopProcess.isSyncProductRelationSuccess()) {
                    msg = "商品标记同步成功，商品对应关系同步失败";
                } else if (!eshopProcess.isSyncProductMarkSuccess()) {
                    msg = "商品对应关系同步成功，商品标记同步失败";
                }
                ProductManageUtil.modifyCurrentProcessMsg(eshopProcessMap.get(eshopInfo.getOtypeId()).getProcessLogger(), msg);
            } else if (msgType == ProcessMessageType.SYNC_FAIL) {
                ProductManageUtil.modifyCurrentProcessMsg(eshopProcessMap.get(eshopInfo.getOtypeId()).getProcessLogger(), msg);
            } else if (msgType == ProcessMessageType.FINISH) {
                ProductManageUtil.processFinish(eshopProcessMap.get(eshopInfo.getOtypeId()).getProcessLogger());
            }
        }
    }

    private void executeSyncProductMark(EshopInfo eshopInfo, List<EshopProductMark> productMarks, EshopProcess eshopProcess, boolean start, boolean end) {
        try {
            if (start) {
                ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), "开始同步商品标记");
            }
            if (CollectionUtils.isNotEmpty(productMarks)) {
                //构建子店铺标记数据
                List<String> uniqueIds = new ArrayList<>();
                List<EshopProductMarkData> productMarkDataList = new ArrayList<>();
                for (EshopProductMark productMark : productMarks) {
                    productMark.setEshopId(eshopInfo.getOtypeId());
                    productMark.setId(UId.newId());
                    productMark.setUniqueId(Md5Utils.md5(String.format("%s%s%s%s", CurrentUser.getProfileId(), productMark.getEshopId(), productMark.getPlatformNumId(), productMark.getPlatformPropertiesName())));
                    if (productMark.getMarkDataId() != null && productMark.getMarkDataId().longValue() > 0 && StringUtils.isNotEmpty(productMark.getProductMarkBigData())) {
                        EshopProductMarkData productMarkData = new EshopProductMarkData();
                        productMarkData.setId(UId.newId());
                        productMarkData.setMarkId(productMark.getId());
                        productMarkData.setBigData(productMark.getProductMarkBigData());
                        productMarkData.setProfileId(productMark.getProfileId());
                        productMarkDataList.add(productMarkData);
                    }
                    uniqueIds.add(productMark.getUniqueId());
                }
                //删除子店历史商品标记及bigdata
                mapper.deleteProductMarks(CurrentUser.getProfileId(), uniqueIds);
                //保存子店商品标记
                markMapper.bulkInsertProductMark(productMarks);
                //保存子店商品标记bigData
                if (CollectionUtils.isNotEmpty(productMarkDataList)) {
                    markBigDataMapper.bulkInsertProductMarkData(productMarkDataList);
                }
            }
            if (end) {
                ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), "同步商品标记结束");
            }
        } catch (Exception ex) {
            eshopProcess.setSyncProductMarkSuccess(false);
            logger.error("账套ID：{}，店铺ID：{},子店铺ID：{},同步标记到子店铺出错：{}", CurrentUser.getProfileId(), eshopInfo.getMainOtypeId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
            ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), String.format("同步商品标记失败，失败原因：%s", ex.getMessage()));
        }
    }

    private void executeSyncProductRelation(EshopInfo eshopInfo, List<EshopProductSkuMapping> eshopProductSkuMappings, EshopProcess eshopProcess, boolean start, boolean end) {
        try {
            if (start) {
                ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), "开始同步商品对应关系");
            }
            BigInteger eshopId = eshopInfo.getOtypeId();
            List<String> uniqueIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(eshopProductSkuMappings)) {
                for (EshopProductSkuMapping eshopProductSkuMapping : eshopProductSkuMappings) {
                    eshopProductSkuMapping.setEshopId(eshopId);
                    eshopProductSkuMapping.setId(UId.newId());
                    eshopProductSkuMapping.setUniqueId(Md5Utils.md5(String.format("%s%s%s%s", CurrentUser.getProfileId(), eshopProductSkuMapping.getEshopId(), eshopProductSkuMapping.getPlatformNumId(), eshopProductSkuMapping.getPlatformPropertiesName())));
                    uniqueIds.add(eshopProductSkuMapping.getUniqueId());
                }
                mapper.batchInsertOrUpdateProductSkuMapping(eshopProductSkuMappings);
                mapper.updateSkuExpandMappingType(CurrentUser.getProfileId(), uniqueIds, MappingType.NOMARL.getCode());
                LogService.addRange(EshopProductMappingLogService.toMappingLogList(eshopProductSkuMappings,
                        ProductOperateLogType.SYNC_RELATION_TO_BRANCH));
            }
        } catch (Exception ex) {
            eshopProcess.setSyncProductRelationSuccess(false);
            logger.error("账套ID：{}，店铺ID：{},子店铺ID：{},同步商品对应关系到子店铺出错：{}", CurrentUser.getProfileId(),
                    eshopInfo.getMainOtypeId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
            ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), String.format("同步商品对应关系失败，失败原因：%s", ex.getMessage()));
        }
        if (end) {
            ProductManageUtil.modifyCurrentProcessMsg(eshopProcess.getProcessLogger(), "同步商品对应关系完成");
        }
    }
}
