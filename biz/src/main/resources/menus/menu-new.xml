<?xml version="1.0" encoding="UTF-8"?>
<root>
    <menus identifier='new'>

        <menu name="网店" path="" isnew="true" position="886" icon='bicon-wangdian'>
            <menu name="基础设置" path="" isnew="true" position="886-1">
                <menu name="网店设置" key="eshoporder.eshopInfoPage"
                      permission-key="eshoporder.eshop.view"
                      path="sale/eshoporder/eshop/EShopList.gspx"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      isnew="true"
                      position="886-1-1" icon="bicon-xiaoshoujigou">
                    <permission key="eshoporder.eshop.view" roles="运营" name="查看"/>
                    <permission key="eshoporder.eshop.add" roles="运营" name="新增" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.edit" roles="运营" name="修改" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.saleRegister" roles="运营" name="营销活动报名"
                                depend="eshop.view"/>
                    <permission key="eshoporder.eshop.delete" roles="运营" name="删除" depend="eshop.view,eshop.view"/>
                    <permission key="eshoporder.eshop.auth" roles="运营" name="授权" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.cancelauth" roles="运营" name="取消授权" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.defaultConfig" roles="运营" name="通用设置" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.open" roles="运营" name="启用" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.stop" roles="运营" name="停用" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.editClassification" roles="运营" name="编辑分类"
                                depend="eshop.view"/>
                    <!--全渠道门店对应权限-->
                    <!--<permission key="eshoporder.eshopCorrespond.view" name="全渠道门店查看"/>
                    <permission key="eshoporder.eshopCorrespond.set" name="全渠道门店设置"/>-->
                    <!--网店仓库对应权限-->
                    <permission key="eshoporder.onlineStoreWarehouse.view" name="网店仓库对应查看"/>
                    <permission key="eshoporder.onlineStoreWarehouse.set" name="网店仓库对应设置"/>
                    <!--平台仓库对应权限-->
                    <!--<permission key="eshoporder.platformWarehouseCorrespond.view" name="平台仓库查看"/>
                    <permission key="eshoporder.platformWarehouseCorrespond.set" name="平台仓库设置"/>-->
                    <!--商城扣费设置权限-->
                    <permission key="eshoporder.mallDeductionFeeSetting.view" name="商城扣费设置查看"/>
                    <permission key="eshoporder.mallDeductionFeeSetting.edit" name="商城扣费设置设置"/>
                    <!--物流对应设置权限-->
                    <permission key="eshoporder.freightMapping.view" name="物流对应查看"/>
                    <permission key="eshoporder.freightMapping.edit" name="物流对应设置"/>
                    <permission key="eshoporder.stateSubsidies.edit" name="国补业务设置"/>
                </menu>
                <menu name="网店商品管理" key="eshoporder.product"
                      function="SaleMiddlegroundFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.product.view"
                      path="sale/eshoporder/product/list.gspx?mode=ptype"
                      position="886-1-2" icon="bicon-baobeishangxiajia">
                    <permission key="eshoporder.product.view" roles="客服,运营" name="查看"/>
                    <permission key="eshoporder.product.relation" roles="客服,运营" name="商品对应"/>
                    <permission key="eshoporder.product.refresh" roles="客服,运营" name="刷新商品"/>
                    <permission key="eshoporder.product.delete" roles="客服,运营" name="删除商品"/>
                    <permission key="eshoporder.product.createLocal" roles="客服,运营" name="生成系统商品"/>
                    <permission key="eshoporder.product.downloadMainPic" roles="客服,运营"
                                name="下载网店商品主图做商品主图"/>
                    <permission key="eshoporder.product.mark.edit" roles="客服,运营" name="标记"
                                depend="eshoporder.product.view"/>
                    <permission key="eshoporder.product.xcode.edit" roles="客服,运营" name="修改商家编码"
                                depend="eshoporder.product.view"/>
                    <permission key="eshoporder.stock.sync" name="库存同步"/>
                </menu>
                <menu name="平台客户对应" key="eshoporder.platformBtypeMap" hideMore="true"
                      path="sale/eshoporder/basic/BtypeMappingList.gspx"
                      permission-key="eshoporder.platformBtypeMap.view"
                      position="886-1-3" icon="bicon-kehugongyingshang"
                      function="EshopFunc||CloudOrderMiddlegroundFunc">
                    <permission key="eshoporder.platformBtypeMap.view" roles="运营" name="查看"/>
                    <permission key="eshoporder.platformBtypeMap.add" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="增"/>
                    <permission key="eshoporder.platformBtypeMap.bind" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="选"/>
                    <permission key="eshoporder.platformBtypeMap.clear" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="解"/>
                    <permission key="eshoporder.platformBtypeMap.batch" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="批量操作"/>
                    <permission key="eshoporder.platformBtypeMap.refresh" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="刷新"/>
                </menu>
<!--                <menu name="网店库存同步" key="eshoporder.stock"-->
<!--                      function="SaleMiddlegroundFunc||EshopFunc||CloudOrderMiddlegroundFunc"-->
<!--                      permission-key="eshoporder.stock.view"-->
<!--                      path="sale/eshoporder/product/list.gspx?mode=stock"-->
<!--                      position="886-1-4" icon="bicon-kucuntongbu">-->
<!--                    <permission key="eshoporder.stock.view" name="查看"/>-->
<!--                    <permission key="eshoporder.stock.sync" name="库存同步"/>-->
<!--                    <permission key="eshoporder.stock.rule.config" name="库存同步规则配置"/>-->
<!--                </menu>-->
                <menu name="直播设置" key="eshoporder.eshopBroadcastSession" hideMore="true"
                      path="sale/eshoporder/eshopsaleorder/BroadcastSessionList.gspx"
                      permission-key="eshoporder.eshopBroadcastSession.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-1-5" icon="bicon-baobeidabiao">
                    <permission key="eshoporder.eshopBroadcastSession.view" roles="客服,运营" name="查看"/>
                    <permission key="eshoporder.eshopBroadcastSession.add" name="新增"/>
                    <permission key="eshoporder.eshopBroadcastSession.modify" name="修改"/>
                    <permission key="eshoporder.eshopBroadcastSession.delete" name="删除"/>
                    <permission key="eshoporder.eshopBroadcastSession.copy" name="复制新增"/>
                </menu>
                <menu name="超卖设置" key="eshoporder.stock.oversold.config" hideMore="true"
                      path="sale/eshoporder/stock/OversoldConfig.gspx"
                      function="EshopFunc"
                      permission-key="eshoporder.stock.oversold.config.view" position="886-1-6"
                      icon="bicon-kucuntongbu">
                    <permission key="eshoporder.stock.oversold.config.view" roles="仓管,运营" name="查看"/>
                    <permission key="eshoporder.stock.oversold.config.edit" roles="仓管,运营" name="设置"/>
                </menu>
            </menu>
            <menu name="订单管理" isnew="true" position="886-2">
                <menu name="网店原始订单" key="eshoporder.eshopSaleOrderPage"
                      permission-key="eshoporder.eshopsaleorder.view"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/eshoporder/eshopsaleorder/EShopSaleOrderListNew.gspx?closeType=closeAll"
                      position="886-2-1" icon="bicon-pingtaidingdan">
                    <permission key="eshoporder.eshopsaleorder.view" roles="审单员,仓管" name="查看"/>
                    <permission key="eshoporder.eshopsaleorder.downloadOrder" roles="审单员" name="下载订单"
                                depend="eshoporder.eshopsaleorder.view"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.downloadById" roles="审单员" name="按单号下载"
                                depend="eshoporder.eshopsaleorder.view"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.download" name="手工下载" roles="审单员"
                                depend="eshoporder.eshopsaleorder.view"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.autoDownload" name="自动下载" roles="审单员"
                                depend="eshoporder.eshopsaleorder.view"/>
                    <permission key="eshoporder.eshopsaleorder.updateOrderAllData" roles="审单员"
                                name="重新下载"/>
                    <permission key="eshoporder.eshopsaleorder.submit" roles="审单员" name="提交订单"/>
                    <permission key="eshoporder.eshopsaleorder.relation" roles="审单员" name="快速对应"/>
                    <permission key="eshoporder.eshopsaleorder.relation.add" roles="审单员" name="快速对应-增"
                                value="true"/>
                    <permission key="eshoporder.eshopsaleorder.submitConfig" roles="审单员" name="提交说明"/>
                    <permission key="eshoporder.eshopsaleorder.export" roles="审单员" name="导出"/>
                    <permission key="eshoporder.eshopCreateOrderPage.view" roles="审单员" name="新增订单"/>
                    <permission key="eshoporder.eshopCreateOrderPage.save" roles="审单员" name="保存原始订单"
                                depend="eshopCreateOrderPage.view"/>
                    <permission key="eshoporder.eshopCreateOrderPage.gift" name="标记/取消赠品"
                                depend="eshopCreateOrderPage.view"/>
                    <permission key="eshoporder.eshopEditOrderPage.view" roles="审单员" name="编辑订单"/>
                    <permission key="eshoporder.eshopImportOrderPage.view" roles="审单员" name="导入订单"/>
                    <permission key="eshoporder.eshopsaleorder.viewOnlineOrder" roles="审单员" name="查看网店订单"/>
                    <permission key="eshoporder.eshopsaleorder.copy" roles="审单员" name="复制该字段"/>
                    <permission key="eshoporder.eshopsaleorder.platformBtypeMapBind"
                                depend="eshoporder.platformBtypeMap.view" roles="审单员,财务,运营"
                                name="客户对应"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshopsaleorder.copyBuyerInfo" roles="审单员,仓管,打单员" value="true"
                                name="复制买家信息"/>
                    <permission key="eshoporder.eshopsaleorder.deleteOrRecoveryOrder" roles="审单员,财务,运营"
                                name="删除/撤销删除订单"/>
                    <permission key="eshoporder.eshopordergather.view" roles="审单员" name="订单收款"/>
                </menu>
                <menu name="销售订单管理" key="eshoporder.eshopCreateAdvanceOrderPage"
                      icon="bicon-yushoudingdanguanli"
                      permission-key="eshoporder.eshopAdvanceSaleOrder.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/eshoporder/advance/EshopAdvanceSaleOrderList.gspx?caption=预售订单处理&amp;tag=AUDIT&amp;closeType=url"
                      position="886-2-2">
                    <permission key="eshoporder.eshopAdvanceSaleOrder.view" roles="审单员,仓管" name="查看"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.submit" roles="审单员" name="确认提交"/>
                    <!--                    <permission key="eshoporder.eshopAdvanceSaleOrder.submitConfig" roles="审单员" name="自动提交规则"/>-->
                    <permission key="eshoporder.eshopAdvanceSaleOrder.add" roles="审单员" name="新增预售订单"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.export" roles="审单员" name="导出"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.updatePlanSendTime" roles="审单员"
                                name="修改预计发货时间"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.updateSellerMemo" roles="审单员"
                                name="改卖家备注(旗帜)"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.copy" roles="审单员" name="复制该字段"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.viewOnlineOrder" roles="审单员"
                                name="查看网店订单"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.update" roles="审单员" name="更新"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.mark" roles="审单员" name="自定义标记"
                                value="true"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.copyBuyerInfo" roles="审单员,仓管,打单员"
                                value="true"
                                name="复制买家信息"/>
                </menu>
                <menu name="订单发货处理-研发中" icon="bicon-xiaoshoudingdanshenhe" key="deliverDeal"
                      permission-key="jarvis.deliverAudit.view,jarvis.simplePrintAndDeliver.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/billManagement/DeliverBillManagement.gspx?caption=订单发货处理&amp;tag=DEAL&amp;closeType=closeAll"
                      target="iframe" isnew="true" position="886-2-3">
                </menu>
                <menu name="订单审核-这个是权限点-研发中" icon="bicon-xiaoshoudingdanshenhe" key="deliverAudit"
                      permission-key="jarvis.deliverAudit.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/billManagement/DeliverBillManagement.gspx?caption=订单审核&amp;tag=AUDIT&amp;closeType=url"
                      hidden="true"
                      target="iframe" isnew="true" position="886-2-4">

                    <permission key="jarvis.deliverAudit.view1" roles="仓管,审单员" name="待整理-李苗祎"/>
                    <permission key="jarvis.deliverAudit.view" roles="仓管,审单员" name="查看"/>
                    <permission key="jarvis.deliverAudit.batchAddPtype" roles="审单员" name="增商品"/>
                    <permission key="jarvis.deliverAudit.batchUpdatePtype" roles="审单员" name="换商品"/>
                    <permission key="jarvis.deliverAudit.batchDelPtype" roles="审单员" name="删商品"/>
                    <permission key="jarvis.deliverAudit.modifyDetail" roles="审单员" name="修改明细信息"/>
                    <permission key="jarvis.deliverCommon.splitByHandel" roles="审单员" name="手工拆分"/>
                    <permission key="jarvis.deliverAudit.cancelSplit" name="取消拆分" roles="审单员"/>
                    <permission key="jarvis.deliverAudit.splitByAuto" roles="审单员" name="策略拆分"/>
                    <permission key="jarvis.deliverAudit.mergeByHandle" roles="审单员" name="手工合并"/>
                    <permission key="jarvis.deliverAudit.cancelMerge" name="取消合并" roles="审单员"/>
                    <permission key="jarvis.deliverAudit.mergeByAuto" roles="审单员" name="策略合并"/>
                    <permission key="jarvis.deliverAudit.modifySeller" roles="审单员" name="改备注信息(旗帜)"/>
                    <include-permission include-key="jarvis.sendDeliver.modifyInvoice"/>
                    <permission key="jarvis.deliverAudit.modifySender" roles="审单员" name="改寄件信息"/>
                    <permission key="jarvis.deliverAudit.modifyReceiver" roles="审单员" name="改收件信息"/>
                    <permission key="jarvis.deliverAudit.viewLog" roles="审单员" name="查看操作日志"/>
                    <permission key="jarvis.deliverAudit.showAdvancePayment" roles="审单员" name="查看预收款"/>
                    <permission key="jarvis.deliverAudit.copyValue" roles="审单员" name="复制该字段"/>

                    <permission key="jarvis.deliverCommon.matchStrategy" roles="审单员" name="策略匹配"/>
                    <permission key="jarvis.deliverAudit.autoAudit" roles="审单员" name="策略审核"/>
                    <permission key="jarvis.deliverAudit.handAudit" roles="审单员" name="手工审核"/>
                    <permission key="jarvis.deliverAudit.forceAudit" roles="审单员" name="强制审核" value="true"
                                depend="jarvis.deliverAudit.handAudit"/>

                    <permission key="jarvis.deliverAudit.mark" roles="审单员" name="自定义标记"/>
                    <permission key="jarvis.deliverAudit.markConfig" roles="审单员" name="标记管理"/>

                    <permission key="jarvis.deliverAudit.updateDeliverBill" roles="审单员" name="还原单据"/>
                    <permission key="jarvis.deliverAudit.resetAndupdateDeliverBill" roles="审单员" name="更新并还原"/>
                    <permission key="jarvis.deliverCommon.deleteDeliverBill" roles="审单员" name="删除单据"/>
                    <permission key="jarvis.deliverCommon.recoverDeletedBill" roles="审单员" name="取消删除单据"/>

                    <permission key="jarvis.deliverCommon.lockDeliverBill" roles="审单员" name="截停单据"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBillConfig" roles="审单员" name="截停说明配置"/>
                    <permission key="jarvis.deliverCommon.cancelLock" roles="审单员" name="取消截停"/>
                    <permission key="jarvis.deliverCommon.lockSendQty" roles="审单员" name="可发货库存锁定"
                                value="true"/>
                    <permission key="jarvis.deliverCommon.cancelLockSendQty" roles="审单员" name="取消可发货库存锁定"
                                value="true"/>
                    <!--                    <permission key="jarvis.deliverCommon.modifyAuthor" roles="审单员" name="改经手人"/>-->
                    <permission key="jarvis.deliverCommon.modifyBusinessSelfDeliver" roles="审单员" name="改发货信息"/>
                    <include-permission include-key="jarvis.deliverPrint.modifyDeliverTemplate"/>
                    <permission key="jarvis.deliverAudit.importFreight" roles="审单员" name="导入修改订单"/>
                    <permission key="jarvis.deliverCommon.batchModify" roles="仓管,打单员" name="改单据信息"/>
                    <permission key="jarvis.deliverCommon.modifyKType" roles="仓管,打单员" name="修改仓库"/>
                    <permission key="jarvis.deliverCommon.modifyDeliverProcessType" roles="仓管,打单员"
                                name="修改发货流程管理方式"/>
                    <permission key="jarvis.deliverCommon.modifyFreight" roles="仓管,审单员" name="修改物流信息"/>
                    <permission key="jarvis.deliverCommon.modifySupplier" roles="审单员" name="修改发货供应商"/>
                    <permission key="jarvis.deliverCommon.export" roles="仓管,打单员" name="导出"/>
                    <permission key="jarvis.deliverCommon.copyReceiveInfo" roles="审单员,仓管,打单员"
                                name="复制收货信息"/>
                    <include-permission include-key="jarvis.deliverCommon.doRelation"/>
                    <permission key="jarvis.deliverCommon.viewPriceAndTotal" roles="审单员,仓管"
                                name="查看单价和金额" value="true"/>
                    <include-permission include-key="jarvis.deliverCommon.contactBuyer"/>

                    <permission key="sale.buyer.cryptograph.view" roles="仓管,审单员" name="明文显示收货信息"
                                value="true"/>

                    <permission key="jarvis.deliverCommon.modifyDisedTaxPrice" roles="仓管,审单员" name="改折后含税单价"
                    />
                    <permission key="jarvis.deliverCommon.modifyPurchasePrice" roles="仓管,审单员" name="改采购单价"
                    />
                    <permission key="jarvis.deliverCommon.modifyBroadcast" roles="仓管,审单员" name="改直播场次信息"
                    />
                    <permission key="jarvis.deliverCommon.importModifyBill" roles="仓管,打单员"
                                name="导入修改单据信息"/>
                    <permission key="jarvis.deliverCommon.checkNotSendArea" roles="仓管,打单员,发货员"
                                name="物流不达检查"/>
                    <permission key="jarvis.deliverCommon.confirmOrder" roles="仓管,审单员" name="接单"/>
                    <permission key="jarvis.deliverCommon.syncPlatformEnclosure" roles="仓管,审单员"
                                name="上传发货要求"/>
                    <permission key="jarvis.deliverAudit.delayDelivery" roles="审单员" name="延迟发货" value="true"/>
                    <permission key="jarvis.deliverAudit.cancelDelayDelivery" roles="审单员" name="取消延迟发货"
                                value="true"/>
                    <permission key="jarvis.deliverCommon.uploadSnToOrders" roles="仓管,审单员"
                                name="平台验证SN"/>
                    <permission key="jarvis.deliverAudit.modifyMainBill" roles="仓管,审单员"
                                name="更换主单号"/>
                    <permission key="jarvis.deliverAudit.modifyQIC" roles="仓管,审单员"
                                name="修改QIC质检方式"/>
                    <permission key="jarvis.deliverAudit.modifyRelation" roles="仓管,审单员"
                                name="更新网店商品与商品对应关系"/>
                    <!--                    <permission key="jarvis.deliverAudit.editConsumables" roles="仓管,审单员"-->
                    <!--                                name="编辑耗材"/>-->
                    <permission key="jarvis.deliverAudit.eshopBillConfig" roles="审单员"
                                name="网店单据设置"/>
                    <permission key="jarvis.deliverCommon.getFreightBillNoAudit" roles="仓管,审单员"
                                name="获取物流单号"/>
                    <permission key="jarvis.deliverCommon.syncWayBill" roles="仓管,审单员" name="同步单号"/>
                    <permission key="jarvis.deliverCommon.cancelFreightBillNoAudit" roles="仓管,审单员"
                                name="取消物流单号"/>
                    <permission key="jarvis.deliverCommon.copyCreateNewSaleOrder" roles="仓管,打单员"
                                name="复制新增订单"/>
                </menu>
                <menu name="订单发货查询-研发中" icon="bicon-yifahuodingdanchaxun" key="sendDeliver.view"
                      permission-key="jarvis.sendDeliver.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/billManagement/DeliverBillQueryList.gspx?caption=订单发货查询&amp;tag=QUERY&amp;closeType=url"
                      target="iframe" isnew="true" position="886-2-4">
                    <!--                    待整理-李苗祎-->
                    <permission key="jarvis.sendDeliver.view" roles="客服,仓管,审单员" name="查看"/>
                    <permission key="jarvis.sendDeliver.copyValue" roles="客服,仓管,审单员" name="复制该字段"/>
                    <permission key="jarvis.deliverCommon.rejectUnSend" roles="仓管,打单员,发货员"
                                name="驳回审核(未出库单据)"/>
                    <permission key="jarvis.deliverCommon.rejectShipped" roles="仓管,打单员,发货员"
                                name="驳回审核(已出库未核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAudit" roles="仓管,打单员,发货员"
                                name="驳回审核(已财务核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAuditConfig" roles="仓管,打单员,发货员"
                                name="驳回原因配置"/>
                    <permission key="jarvis.sendDeliver.viewLog" roles="仓管,审单员" name="查看操作日志"/>
                    <permission key="jarvis.sendDeliver.showAdvancePayment" roles="仓管,审单员" name="查看预收款"/>
                    <permission key="jarvis.deliverCommon.syncWayBill" roles="仓管,审单员" name="同步单号"/>
                    <permission key="jarvis.sendDeliver.importFreight" name="导入修改订单" roles="仓管,审单员"/>
                    <include-permission include-key="jarvis.deliverCommon.export"/>
                    <permission key="jarvis.sendDeliver.modifyDetail" roles="仓管,审单员" name="修改明细信息"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBill" roles="仓管,审单员" name="截停单据"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBillConfig" roles="审单员" name="截停说明配置"/>
                    <permission key="jarvis.deliverCommon.cancelLock" roles="仓管,审单员" name="取消截停"/>
                    <permission key="jarvis.sendDeliver.rePost" name="重新核算" roles="仓管,审单员,打单员"/>
                    <permission key="jarvis.sendDeliver.rePostSetBtn" name="核算后单据时间设置"
                                roles="仓管,审单员,打单员"/>
                    <permission key="jarvis.deliverCommon.modifyFreight" roles="仓管,审单员" name="修改物流信息"/>
                    <permission key="jarvis.deliverCommon.copyReceiveInfo" roles="仓管,打单员" name="复制收货信息"/>
                    <permission key="jarvis.deliverQuery.mark" roles="审单员" name="自定义标记"/>
                    <permission key="jarvis.deliverQuery.markConfig" roles="审单员" name="标记管理"/>
                    <permission key="jarvis.deliverCommon.viewPriceAndTotal" roles="审单员,仓管" name="查看单价和金额"
                                value="true"/>
                    <include-permission include-key="eshoporder.eshoprefund.add"/>
                    <permission key="jarvis.deliverCommon.contactBuyer" roles="审单员" name="联系买家"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <include-permission include-key="jarvis.deliverAudit.modifySeller"/>
                    <permission key="jarvis.sendDeliver.modifyInvoice" roles="仓管,审单员" name="改发票信息"/>
                    <!--<permission key="jarvis.sendDeliver.showInvoiceDecrypt" roles="仓管,审单员" name="明文显示发票信息"/>-->
                    <permission key="jarvis.sendDeliver.reSubmitInvoice" roles="仓管,审单员" name="提交发票"/>
                    <permission key="jarvis.sendDeliver.uploadInvoice" roles="审单员" name="上传发票"/>
                    <permission key="jarvis.deliverCommon.modifyDisedTaxPrice" roles="仓管,审单员"
                                name="改折后含税单价"/>
                    <include-permission include-key="jarvis.deliverCommon.syncPlatformEnclosure"/>
                    <!--                    <include-permission include-key="jarvis.deliverCommon.showBuyerInfo"/>-->
                    <include-permission include-key="jarvis.deliverCommon.uploadSnToOrders"/>
                    <permission key="jarvis.sendDeliver.reverseAccount" roles="审单员" name="驳回库存待核算"
                                value="false"/>
                    <permission key="jarvis.deliverCommon.batchUpdateMessageAndMemoMark" roles="仓管,审单员"
                                name="批量更新留言备注"/>
                    <permission key="jarvis.deliverCommon.batchUpdateAddressMark" roles="仓管,审单员"
                                name="批量更新地址"/>
                    <permission key="jarvis.deliverCommon.batchUpdateInvoiceMark" roles="仓管,审单员"
                                name="批量更新发票"/>
                    <permission key="jarvis.deliverCommon.copyCreateNewSaleOrder" roles="仓管,打单员"
                                name="复制新增订单"/>
                    <permission key="jarvis.deliverCommon.batchModify" roles="仓管,打单员" name="改单据信息"/>
                    <permission key="jarvis.deliverCommon.modifyPurchasePrice" roles="仓管,审单员" name="改采购单价"/>
                    <permission key="jarvis.sendDeliver.reSubmitInvoiceConfig" roles="仓管,审单员" name="提交开票配置"/>
                </menu>
                <menu name="订单处理策略-王凯" icon="bicon-dingdanchulicelve" key="deliverBillStrategy"
                      path="sale/jarvis/DeliverBill/deliverStrategy/DeliverStrategyConfig.gspx?key=deliverBillStrategy"
                      function="EshopFunc||CloudOrderMiddlegroundFunc" hidden="true"
                      target="iframe" isnew="true" position="886-2-13">
                    <!--                策略配置流程中的所有权限点，包含原单、仓储的权限点 都收集起来，只保留要用的-->
                    <permission key="jarvis.strategy.ktype" roles="仓管,审单员" name="查看仓库策略"/>
                    <permission key="jarvis.strategy.ktypeEdit" roles="仓管,审单员" name="编辑仓库策略" value="true"/>

                    <permission key="jarvis.strategy.split" roles="仓管,审单员" name="查看拆分策略"/>
                    <permission key="jarvis.strategy.splitEdit" roles="仓管,审单员" name="编辑拆分策略" value="true"/>

                    <permission key="jarvis.strategy.merge" roles="仓管,审单员" name="查看合并策略"/>
                    <permission key="jarvis.strategy.mergeEdit" roles="仓管,审单员" name="编辑合并策略" value="true"/>

                    <permission key="jarvis.strategy.author" roles="仓管,审单员" name="查看经手人策略"/>
                    <permission key="jarvis.strategy.authorEdit" roles="仓管,审单员" name="编辑经手人策略"
                                value="true"/>
                    <permission key="jarvis.strategy.lock" roles="仓管,审单员" name="查看截停策略"/>
                    <permission key="jarvis.strategy.lockEdit" roles="仓管,审单员" name="编辑截停策略" value="true"/>

                    <permission key="jarvis.strategy.mark" roles="仓管,审单员" name="查看标记策略"/>
                    <permission key="jarvis.strategy.markEdit" roles="仓管,审单员" name="编辑标记策略" value="true"/>

                    <!--                <permission key="jarvis.strategy.supplier" roles="仓管,审单员" name="查看供应商策略"/>-->
                    <!--                <permission key="jarvis.strategy.supplierEdit" roles="仓管,审单员" name="编辑供应商策略" value="true"/>-->

                    <permission key="jarvis.strategy.audit" roles="仓管,审单员" name="查看审核策略"/>
                    <permission key="jarvis.strategy.auditEdit" roles="仓管,审单员" name="编辑审核策略" value="true"/>

                    <permission key="jarvis.strategy.mergedCheck" roles="仓管,审单员" name="查看合并检查"/>
                    <permission key="jarvis.strategy.mergedCheckEdit" roles="仓管,审单员" name="编辑合并策略"
                                value="true"/>
                    <permission key="jarvis.strategy.goodsReplace" roles="仓管,审单员" name="查看商品替换策略"/>
                    <permission key="jarvis.strategy.goodsReplaceEdit" roles="仓管,审单员" name="编辑商品替换策略"
                                value="true"/>
                    <!--                <permission key="jarvis.strategy.liveBroadcast" roles="仓管,审单员" name="查看直播策略"/>-->
                    <!--                <permission key="jarvis.strategy.liveBroadcastEdit" roles="仓管,审单员" name="编辑直播策略"-->
                    <!--                            value="true"/>-->
                    <permission key="jarvis.strategy.consumables" roles="仓管,审单员" name="耗材策略"/>

                    <permission key="jarvis.giftRule.view" roles="仓管,运营,审单员" name="查看网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.new" roles="运营,审单员" name="新增网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.edit" roles="运营,审单员" name="编辑网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.delete" roles="运营,审单员" name="删除网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.priority" roles="运营,审单员" name="赠品策略优先级调整"/>
                    <permission key="jarvis.giftRule.group" roles="运营,审单员" name="赠品策略分类管理"/>
                </menu>
            </menu>

            <menu name="售后管理" path="" isnew="true" position="886-3">
                <menu name="售后处理" key="eshoporder.eshopRefundListPage"
                      path="sale/eshoporder/aftersale/AfterSaleDeal.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      cross-server-permission-key="eshoporder.eshoprefund.view"
                      position="886-3-1" icon="bicon-shouhouguanli">
                    <permission key="eshoporder.eshoprefund.view" roles="客服,仓管,审单员" name="查看"/>
                    <permission key="eshoporder.eshoprefund.add" roles="仓管,审单员" name="新增售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.edit" roles="仓管,审单员" name="修改售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.relation.add" roles="仓管,审单员" name="快速对应-增"
                                value="true"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.delete" roles="仓管,审单员" name="删除售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.cancle" roles="仓管,审单员" name="撤销删除"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.audit" roles="仓管,审单员" name="售后审核"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.fileUpload" roles="仓管,审单员" name="附件上传"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.relateCheckin" roles="客服,仓管,审单员"
                                name="关联/取消收货记录"
                                cross-server-depend="eshoporder.eshoprefund.view" value="true"/>
                    <permission key="eshoporder.eshoprefund.updaterefund" roles="客服,仓管,审单员"
                                name="重新下载"
                                value="true"/>
                    <permission key="eshoporder.eshoprefund.refundagree" roles="客服,仓管,审单员" name="同意/拒绝仅退款"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.returnagree" roles="客服,仓管,审单员" name="同意/拒绝退货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.returnrefundagree" roles="客服,仓管,审单员"
                                name="已退货并同意/拒绝退款"
                                cross-server-depend="eshoporder.eshoprefund.view"/>

                    <permission key="eshoporder.eshoprefund.exchangeagree" roles="客服,仓管,审单员" name="同意/拒绝换货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.returnexchangeagree" roles="客服,仓管,审单员"
                                name="已退货并完成/拒绝换货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.processagree" roles="客服,仓管,审单员"
                                name="同意/拒绝补发"/>
                    <permission key="eshoporder.eshoprefund.receive" roles="客服,仓管,审单员" name="生单并入库"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.factoryprocessing" roles="仓管,审单员"
                                name="厂家一并处理售后"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.distributorprocessing" roles="仓管,审单员"
                                name="提交分销商处理"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.yunreceive" roles="客服,仓管,审单员" name="生单并提交云仓"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.deal" roles="仓管,审单员" name="补发生单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.cancelProcess" roles="仓管,审单员" name="取消补发"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.reProcess" roles="仓管,审单员" name="重新补发"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.pay" roles="仓管,审单员" name="退款支付"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.config" roles="仓管,审单员" name="售后业务配置"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.export" roles="仓管,审单员" name="导出"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.editrefundtype" roles="仓管,审单员"
                                name="修改售后类型"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.modifyktype" roles="仓管,审单员" name="修改退货仓库"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.receiveback" roles="仓管,审单员" name="收货回传"
                                cross-server-depend="eshoporder.eshoprefund.view" value="true"/>

                    <permission key="eshoporder.eshoprefund.batchupdate" roles="仓管,审单员" name="批量修改"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.copy" roles="审单员" name="复制该字段"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.relation" roles="仓管,审单员" name="快速对应"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.contact" roles="仓管,审单员" name="联系买家"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshoprefund.buyeredit" roles="仓管,审单员" name="编辑收件人"
                                value="true"/>
                </menu>
                <!--            <menu name="售后查询" key="eshoporder.AftersaleList"-->
                <!--                  path="sale/eshoporder/aftersale/AftersaleList.gspx"-->
                <!--                  function="EshopFunc||CloudOrderMiddlegroundFunc"-->
                <!--                  cross-server-permission-key="eshoporder.aftersale.view"-->
                <!--                  position="886-3-2" icon="bicon-shouhouchaxun">-->
                <!--            </menu>-->
                <menu name="收货登记" key="eshoporder.eshopReceiveCheckInPage"
                      cross-server-permission-key="eshoporder.receivecheckin.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/jarvis/eshoprefund/EshopRefundReceiveCheckInList.gspx" position="886-3-3"
                      icon="bicon-shouhuodengji">
                    <permission key="eshoporder.receivecheckin.view" roles="仓管,审单员" name="查看"/>
                    <permission key="eshoporder.receivecheckin.add" roles="仓管,审单员" name="新增"/>
                    <!--                    <permission key="eshoporder.receivecheckin.edit" roles="仓管,审单员" name="编辑"/>-->
                    <permission key="eshoporder.receivecheckin.delete" roles="仓管,审单员" name="删除"/>
                    <permission key="eshoporder.receivecheckin.relate" roles="仓管,审单员" name="关联售后单"/>
                    <permission key="eshoporder.receivecheckin.otherstockin" roles="仓管,审单员" name="无信息件处理"/>
                    <permission key="eshoporder.receivecheckin.batchCheckin" roles="仓管,审单员" name="批量验货" value="true"/>
                    <permission key="eshoporder.receivecheckin.print" roles="仓管,审单员" name="打印收货登记"
                                value="true"/>
                    <permission key="eshoporder.receivecheckin.saveandprint" roles="仓管,审单员"
                                name="保存并打印收货登记" value="true"/>
                    <permission key="eshoporder.receivecheckin.export" roles="仓管,审单员" name="导出收货登记"
                                value="true"/>
                    <!--                <permission key="receivecheckin.loss" roles="仓管,审单员" name="商品报损" />-->
                    <!--            <permission key="eshoporder.receivecheckin.config" roles="仓管,审单员" name="收货业务配置"/>-->
                </menu>
            </menu>

            <menu name="统计报表" path="" isnew="true" position="886-4">
                <menu name="网店销售统计" key="eShopSaleStatistics"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/EShopSaleStatistics.gspx"
                      position="886-4-1" permission-key="jarvis.eShopSaleStatistics.view"
                      icon="bicon-wangdianyunyingtongji" target="iframe">
                    <permission key="jarvis.eShopSaleStatistics.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.eShopSaleStatistics.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                </menu>
<!--                <menu name="发货物流统计" key="deliveryLogistic"-->
<!--                      function="EshopFunc||CloudOrderMiddlegroundFunc"-->
<!--                      path="/sale/jarvis/report/EShopSaleStatistics.gspx"-->
<!--                      position="886-4-2" permission-key="jarvis.deliveryLogistic.view"-->
<!--                      icon="bicon-wangdianyunyingtongji" target="iframe">-->
<!--                    <permission key="jarvis.deliveryLogistic.view" roles="发货员,审单员,打单员,仓管" name="查看"/>-->
<!--                    <permission key="jarvis.deliveryLogistic.export" roles="发货员,审单员,打单员,仓管" name="导出"/>-->
<!--                </menu>-->
                <menu name="售后统计" key="eshoporder.EShopRefundReportPage"
                      cross-server-permission-key="eshoporder.EShopRefundReportPage.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/eshoporder/eshoprefund/refundreport/RefundReport.gspx"
                      icon="bicon-shouhoumingxitongji" position="886-4-3">
                    <permission key="eshoporder.EShopRefundReportPage.view" roles="运营,财务"
                                name="查看"/>
                </menu>
                <menu name="分销商毛利统计" key="distributionGrossProfitStatistics"
                      permission-key="jarvis.distributionGrossProfitStatistics.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc" target="iframe" isnew="true"
                      path="/sale/jarvis/report/DistributionGrossProfitStatistics.gspx?closeType=closeAll"
                      icon="bicon-wangdianfahuomingxibiao" position="886-4-4">
                    <permission key="jarvis.distributionGrossProfitStatistics.view" roles="审单员,财务" name="查看"/>
                    <permission key="jarvis.distributionGrossProfitStatistics.export" roles="审单员,财务" name="导出"/>
                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.view" roles="审单员,财务"
                                name="查看明细"/>
                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.export" roles="审单员,财务"
                                name="导出明细"/>
                </menu>
            </menu>

        </menu>

        <menu name="销售" position="888" icon='bicon-xiaoshou'>
            <menu name="销售目标" path="" position="888-4">
                <menu name="销售目标管理" key="eshoporder.TaskListNew"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/TaskListNew.gspx" icon="bicon-xiaoshoumubiaoxiankuang"
                      permission-key="eshoporder.TaskListNew.view"
                      position="888-4-1">
                    <permission key="eshoporder.TaskListNew.view" name="查看"/>
                    <permission key="eshoporder.TaskListNew.add" name="新增"/>
                    <permission key="eshoporder.TaskListNew.edit" name="修改"/>
                    <permission key="eshoporder.TaskListNew.delete" name="删除"/>
                    <permission key="eshoporder.TaskListNew.open" name="启用"/>
                    <permission key="eshoporder.TaskListNew.close" name="终止"/>
                    <permission key="eshoporder.TaskListNew.copy" name="复制"/>
                    <permission key="eshoporder.TaskListNew.exec" name="目标执行情况"/>
                </menu>
                <menu name="销售目标统计" key="eshoporder.TaskListNewSummary"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/TaskListNew.gspx" icon="bicon-xiaoshoumubiaoxiankuang"
                      permission-key="eshoporder.TaskListNew.view"
                      position="888-4-2">
                </menu>
            </menu>
        </menu>

        <menu name="库存" position="891" icon="bicon-kucun">
            <menu name="库存报表" position="891-4">
                <menu name="可销售库存查询" icon="bicon-kucunzhuangkuangchaxun" key="analysiscloud.saleStockReport"
                      permission-key="analysiscloud.saleStockReport.view"
                      path="sale/eshoporder/stock/SaleStockReport.gspx"
                      position="891-4-3">
                    <permission key="analysiscloud.saleStockReport.view" name="查看"/>
                    <permission key="analysiscloud.saleStockReport.export" name="导出"
                                depend="analysiscloud.saleStockReport.view"/>
                    <permission key="analysiscloud.saleStockReport.print" name="打印"
                                depend="analysiscloud.saleStockReport.view"/>
                    <permission key="analysiscloud.saleStockReport.ruleConfig" name="可销售库存公式设置"
                                depend="analysiscloud.saleStockReport.view"/>
                </menu>

                <menu name="可发货库存查询" icon="bicon-kucunzhuangkuangchaxun" key="analysiscloud.sendStockReport"
                      permission-key="analysiscloud.sendStockReport.view"
                      path="sale/eshoporder/stock/SendStockReport.gspx"
                      position="891-4-4" function="SendQtyFunc || EshopFunc">
                    <permission key="analysiscloud.sendStockReport.view" name="查看"/>
                    <permission key="analysiscloud.sendStockReport.export" name="导出"
                                depend="analysiscloud.sendStockReport.view"/>
                    <permission key="analysiscloud.sendStockReport.print" name="打印"
                                depend="analysiscloud.sendStockReport.view"/>
                </menu>
            </menu>
        </menu>


        <!-- **************      门店菜单start    **************-->
        <menu name="门店" position="887" icon='bicon-tongji' function="StoreFunc"/>
        <menu name="门店" path="" isnew="true" position="887-1" icon='bicon-tongji' function="StoreFunc">
            <menu name="门店管理" key="shopsale.store" path="sale/shopsale/pages/store/StoreList.gspx" isnew="true"
                  function="StoreFunc||WebRetailBillFunc||OtypeAccountFunc"
                  position="887-1-1" permission-key="shopsale.store.view" icon="bicon-quanqudaomendianduiying">
                <permission key="shopsale.store.view" roles="运营,销售" name="查看"/>
                <permission key="shopsale.store.add" roles="运营,销售" name="新增" depend="shopsale.store.view"/>
                <permission key="shopsale.store.edit" roles="运营,销售" name="修改" depend="shopsale.store.view"/>
                <permission key="shopsale.store.delete" roles="运营,销售" name="删除" depend="shopsale.store.view"/>
                <permission key="shopsale.store.open" roles="运营,销售" name="启用" depend="shopsale.store.view"/>
                <permission key="shopsale.store.stop" roles="运营,销售" name="停用" depend="shopsale.store.view"/>

                <permission key="shopsale.store.setting" roles="运营,销售" name="门店设置"
                            depend="shopsale.store.edit"/>
                <permission key="shopsale.store.allowPosLogin" roles="运营,销售" name="门店登录"
                            depend="shopsale.store.edit"/>
                <permission key="shopsale.store.barcodeScaleConfig" roles="运营,销售" name="条码秤设置"
                            depend="shopsale.store.view"/>
                <permission key="shopsale.store.barcodeScalePtype" roles="运营,销售" name="条码秤商品设置"
                            depend="shopsale.store.view"/>
            </menu>
        </menu>

        <menu name="促销" position="887-2" icon="bicon-dianpujiageben" function="StoreFunc">
            <menu name="促销活动" key="shopsale.promotion"
                  path="sale/shopsale/pages/promotion/PromotionList.gspx?PromotionUseRange=retail"
                  isnew="true" function="StoreFunc"
                  position="887-2-1" permission-key="shopsale.promotion.view"
                  icon="bicon-quanqudaomendianduiying">
                <permission key="shopsale.promotion.view" roles="运营,销售" name="查看"/>
                <permission key="shopsale.promotion.add" roles="运营,销售" name="新增"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.edit" roles="运营,销售" name="修改"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.delete" roles="运营,销售" name="删除"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.open" roles="运营,销售" name="启用"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.stop" roles="运营,销售" name="停用"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.priority" roles="运营,销售" name="促销优先级"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.automation" roles="运营,销售" name="促销配置"
                            depend="shopsale.promotion.view"/>
            </menu>
            <!--                <menu name="云订货促销管理" key="shopsale.promotionCloud"-->
            <!--                      path="sale/shopsale/pages/promotion/PromotionList.gspx?PromotionUseRange=cloudOrdering"-->
            <!--                      isnew="true"-->
            <!--                      position="887-0.6-2" permission-key="shopsale.promotionCloud.view"-->
            <!--                      icon="bicon-quanqudaomendianduiying">-->
            <!--                    <permission key="shopsale.promotionCloud.view" roles="运营,销售" name="查看"/>-->
            <!--                    <permission key="shopsale.promotionCloud.add" roles="运营,销售" name="新增"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.edit" roles="运营,销售" name="修改"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.delete" roles="运营,销售" name="删除"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.open" roles="运营,销售" name="启用"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.stop" roles="运营,销售" name="停用"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.priority" roles="运营,销售" name="促销优先级"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                    <permission key="shopsale.promotionCloud.automation" roles="运营,销售" name="促销配置"-->
            <!--                                depend="shopsale.promotionCloud.view"/>-->
            <!--                </menu>-->

        </menu>

        <menu name="销售" position="888"/>
        <menu name="销售业务" position="888-1"/>
        <menu name="批发促销管理" key="shopsale.promotionWholesale"
              path="sale/shopsale/pages/promotion/PromotionWholesaleList.gspx"
              isnew="false" function="WholeSalePromotionFunc"
              position="888-1-3" permission-key="shopsale.promotion.view"
              icon="bicon-quanqudaomendianduiying">
            <permission key="shopsale.promotion.view" roles="运营,销售" name="查看"/>
            <permission key="shopsale.promotion.add" roles="运营,销售" name="新增"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.edit" roles="运营,销售" name="修改"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.delete" roles="运营,销售" name="删除"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.open" roles="运营,销售" name="启用"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.stop" roles="运营,销售" name="停用"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.priority" roles="运营,销售" name="促销优先级"
                        depend="shopsale.promotion.view"/>
            <permission key="shopsale.promotion.automation" roles="运营,销售" name="促销配置"
                        depend="shopsale.promotion.view"/>
        </menu>

        <menu name="门店统计" position="887-3" icon='bicon-tongji' function="StoreFunc">

            <menu name="门店单据查询" key="shopsale.saleorderquery"
                  path="sale/shopsale/pages/saleorder/SaleOrderQueryRetail.gspx"
                  isnew="true" function="StoreFunc &amp;&amp; !CumsterFunc"
                  position="887-3-1" permission-key="shopsale.saleorderquery.view"
                  icon="bicon-mendiandanjuchaxun">
                <permission key="shopsale.saleorderquery.view" name="查看"/>
                <permission key="shopsale.saleorderquery.edit" name="修改明细" value="true"/>
                <permission key="shopsale.saleorderquery.balance" name="批量结存"/>
                <permission key="shopsale.saleorderquery.export" name="导出"
                            depend="shopsale.saleorderquery.view"/>
                <permission key="shopsale.saleorderquery.delete" name="删除单据"/>
                <permission key="shopsale.saleorderquery.paystate" name="修改支付状态"/>
                <permission key="shopsale.saleorderquery.modifyBill" name="修改单据信息"/>
            </menu>

            <menu name="交接班记录" key="shopsale.shiftchangesrecord"
                  path="sale/shopsale/pages/shiftchange/ShiftChangesRecord.gspx"
                  isnew="true" function="StoreFunc"
                  position="887-3-2" permission-key="shopsale.shiftchangesrecord.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.shiftchangesrecord.view" name="查看"/>
                <permission key="shopsale.shiftchangesrecord.export" name="导出"
                            depend="shopsale.shiftchangesrecord.view"/>
            </menu>
            <menu name="钱箱存取记录" key="shopsale.cashboxrecord"
                  path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx"
                  isnew="true" function="StoreFunc"
                  position="887-3-3" permission-key="shopsale.cashboxrecord.view"
                  icon="bicon-qianxiangcunqujilu">
                <permission key="shopsale.cashboxrecord.view" name="查看"/>
                <permission key="shopsale.cashboxrecord.export" name="导出" depend="shopsale.cashboxrecord.view"/>
            </menu>

            <menu name="门店营业占比" key="shopsale.businessPercentage"
                  path="sale/shopsale/pages/shopreport/BusinessPercentage.gspx"
                  isnew="true" function="StoreFunc"
                  position="887-3-4" permission-key="shopsale.businessPercentage.view"
                  icon="bicon-mendianyingyezhanbi">
                <permission key="shopsale.businessPercentage.view" name="查看"/>
                <permission key="shopsale.businessPercentage.export" name="导出"
                            depend="shopsale.businessPercentage.view"/>
            </menu>
            <menu name="门店收入统计" key="shopsale.incomeStatistics"
                  path="sale/member/pages/statistics/StoreIncomeStatistics.gspx"
                  isnew="true" function="StoreFunc"
                  position="887-3-5" permission-key="shopsale.incomeStatistics.view"
                  icon="bicon-mendianshourutongji">
                <permission key="shopsale.incomeStatistics.view" name="查看"/>
                <permission key="shopsale.incomeStatistics.export" name="导出"
                            depend="shopsale.incomeStatistics.view"/>
            </menu>

            <menu name="直营门店单据查询" key="shopsale.saleorderqueryretail"
                  path="sale/shopsale/pages/saleorder/SaleOrderQuery.gspx?SaleOrderType=DirectSales"
                  isnew="true" function="CumsterFunc"
                  position="887-3-7" permission-key="shopsale.saleorderqueryretail.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.saleorderqueryretail.view" name="查看"/>
                <permission key="shopsale.saleorderqueryretail.superView" name="查看全部"/>
                <permission key="shopsale.saleorderqueryretail.edit" name="修改明细"/>
                <permission key="shopsale.saleorderqueryretail.editOrder" name="修改营业员"/>
                <permission key="shopsale.saleorderqueryretail.balance" name="提交核算"/>
                <permission key="shopsale.saleorderqueryretail.export" name="导出"
                            depend="shopsale.saleorderqueryretail.view"/>
                <permission key="shopsale.saleorderqueryretail.confirmpay" name="确认收款"/>
                <permission key="shopsale.saleorderqueryretail.delete" name="删除单据"/>
                <permission key="shopsale.saleorderqueryretail.viewpayinfo" name="查看账单流水"/>
                <permission key="shopsale.saleorderqueryretail.checking" name="无需对账"/>
                <permission key="shopsale.saleorderqueryretail.cancelChecking" name="取消无需对账"/>
                <permission key="shopsale.saleorderqueryretail.editpay" name="填写支付信息"/>
                <permission key="shopsale.saleorderqueryretail.editpayway" name="编辑支付方式"/>
                <permission key="shopsale.saleorderqueryretail.editoutno" name="编辑支付流水号"/>
                <permission key="shopsale.saleorderqueryretail.editmemo" name="编辑附加说明"/>
                <permission key="shopsale.saleorderqueryretail.editstarttime" name="编辑服务开始时间"/>
                <permission key="shopsale.saleorderqueryretail.editendtime" name="编辑服务结束时间"/>
                <permission key="shopsale.saleorderqueryretail.currencydisedtaxedtotal" name="编辑现价"/>
                <permission key="shopsale.saleorderqueryretail.editcustomsalestage" name="编辑销售类型"/>
                <permission key="shopsale.saleorderqueryretail.upLoadFile" name="上传附件"/>
                <permission key="shopsale.saleorderqueryretail.deleteFile" name="删除附件"/>
            </menu>


            <menu name="渠道门店单据查询" key="shopsale.saleorderquerydistributors"
                  path="sale/shopsale/pages/saleorder/SaleOrderQuery.gspx?SaleOrderType=Distributors"
                  isnew="true" function="ServiceIndustryFunc"
                  position="887-3-8" permission-key="shopsale.saleorderquerydistributors.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.saleorderquerydistributors.view" name="查看"/>
                <permission key="shopsale.saleorderquerydistributors.superView" name="查看全部"/>
                <permission key="shopsale.saleorderquerydistributors.edit" name="修改明细"/>
                <permission key="shopsale.saleorderquerydistributors.editOrder" name="修改营业员"/>
                <permission key="shopsale.saleorderquerydistributors.balance" name="提交核算"/>
                <permission key="shopsale.saleorderquerydistributors.export" name="导出"
                            depend="shopsale.saleorderquerydistributors.view"/>
                <permission key="shopsale.saleorderquerydistributors.confirmpay" name="确认收款"/>
                <permission key="shopsale.saleorderquerydistributors.delete" name="删除单据"/>
                <permission key="shopsale.saleorderquerydistributors.viewpayinfo" name="查看账单流水"/>
                <permission key="shopsale.saleorderquerydistributors.checking" name="无需对账"/>
                <permission key="shopsale.saleorderquerydistributors.cancelChecking" name="取消无需对账"/>
                <permission key="shopsale.saleorderquerydistributors.editpay" name="填写支付信息"/>
                <permission key="shopsale.saleorderquerydistributors.editpayway" name="编辑支付方式"/>
                <permission key="shopsale.saleorderquerydistributors.editoutno" name="编辑支付流水号"/>
                <permission key="shopsale.saleorderquerydistributors.commission" name="修改分佣比例"/>
                <permission key="shopsale.saleorderquerydistributors.editmemo" name="编辑附加说明"/>
                <permission key="shopsale.saleorderquerydistributors.editcustomsalestage" name="编辑销售类型"/>
                <permission key="shopsale.saleorderquerydistributors.invoice" name="批量提交开票"/>
                <permission key="shopsale.saleorderquerydistributors.upLoadFile" name="上传附件"/>
                <permission key="shopsale.saleorderquerydistributors.deleteFile" name="删除附件"/>
            </menu>
        </menu>

        <menu name="钱箱" position="887-900" icon='bicon-tongji' function="StoreFunc">
            <menu name="钱箱" key="shopsale.cashbox" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-900-1" permission-key="shopsale.cashbox.open"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.cashbox.open" name="打开钱箱"/>
            </menu>
        </menu>

        <menu name="交接班" position="887-991" icon='bicon-tongji'>
            <menu name="交接班" key="shopsale.shiftchanges" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-991-1" permission-key="shopsale.shiftchanges.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.shiftchanges.view" name="查看"/>
                <permission key="shopsale.shiftchanges.saledetail" name="查看销售额明细"/>
                <permission key="shopsale.shiftchanges.print" name="交接时打印小票"/>
                <permission key="shopsale.shiftchanges.out" name="交接并登出"/>
            </menu>
        </menu>

        <menu name="POS配置" position="887-992" icon='bicon-tongji' function="StoreFunc">
            <menu name="要货申请" key="shopsale.transferOrder" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-1" permission-key="shopsale.transferOrder.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.transferOrder.view" name="查看"/>
                <permission key="shopsale.transferOrder.create" name="新增"/>
                <permission key="shopsale.transferOrder.edit" name="编辑"/>
                <permission key="shopsale.transferOrder.confirm" name="提交"/>
                <permission key="shopsale.transferOrder.send" name="发货"/>
                <permission key="shopsale.transferOrder.delete" name="删除"/>
                <permission key="shopsale.transferOrder.print" name="打印"/>
            </menu>
            <menu name="调货管理" key="shopsale.tenderManage" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-2" permission-key="shopsale.tenderManage.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.tenderManage.view" name="查看"/>
                <permission key="shopsale.tenderManage.confirm" name="确认入库"/>
                <permission key="shopsale.tenderManage.autoPrint" name="确认后自动打印单据"/>
                <permission key="shopsale.tenderManage.columnConfig" name="列配置"/>
                <permission key="shopsale.tenderManage.print" name="打印"/>
            </menu>
            <menu name="通用设置" key="shopsale.setting" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-3" permission-key="shopsale.setting.edit"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.setting.edit" name="编辑"/>
                <permission key="shopsale.hotkey.edit" name="快捷键设置" value="true"/>
            </menu>
            <menu name="打印设置" key="shopsale.print" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-4" permission-key="shopsale.print.edit" icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.print.edit" name="编辑"/>
                <permission key="shopsale.print.patchworknote" name="补打小票" roles="销售"/>
            </menu>
            <menu name="副屏设置" key="shopsale.screen" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-5" permission-key="shopsale.screen.edit"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.screen.edit" name="编辑"/>
            </menu>
            <menu name="打印模版配置" key="shopsale.printmodel" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-6" permission-key="shopsale.printmodel.edit"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.printmodel.edit" name="编辑"/>
            </menu>
            <menu name="开单设置" key="shopsale.salesetting" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-7" permission-key="shopsale.salesetting.editDetailDiscount"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.salesetting.editDetailDiscount" name="编辑明细折扣" value="true"/>
                <permission key="shopsale.salesetting.editDetailPrice" name="编辑明细售价" value="true"/>
                <permission key="shopsale.salesetting.editOrderPreferential" name="总额优惠" value="true"/>
                <permission key="shopsale.salesetting.editGift" name="设置赠品" value="true"/>
                <permission key="shopsale.salesetting.saveGraft" name="挂单" value="true"/>
                <permission key="shopsale.salesetting.getGraft" name="取单" value="true"/>
                <permission key="shopsale.salesetting.billDiscount" name="整单折扣" value="true"/>
                <permission key="shopsale.salesetting.editRetailPrice" name="修改零售价" value="true"/>
                <permission key="shopsale.salesetting.backGoods" name="按商品退货" value="true"/>
                <permission key="shopsale.salesetting.viewBatchCost" name="查看批次成本"/>
            </menu>
            <menu name="储值记录" key="shopsale.rechargeRecord" function="StoreFunc" isnew="true" hidden="true"
                  position="887-992-8" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx"
                  permission-key="shopsale.rechargeRecord.view" icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.rechargeRecord.view" name="查看"/>
                <permission key="shopsale.rechargeRecord.invalidate" name="作废" value="true"/>
            </menu>
            <menu name="会员储值" key="shopsale.recharge" function="StoreFunc"
                  isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                  position="887-992-9" permission-key="shopsale.recharge.view"
                  icon="bicon-xiaoshoukaipiaodanjuchaxun">
                <permission key="shopsale.recharge.view" name="会员充值" value="true"/>
                <permission key="shopsale.recharge.manual" name="手工充值" value="true"/>
                <permission key="shopsale.recharge.gift" name="充值赠送" value="true"
                            depend="shopsale.recharge.manual"/>
            </menu>
        </menu>
        <!--**************       门店菜单end     **************-->

        <!-- *************      会员菜单start   **************-->
        <menu name="客户" position="888.7"/>
        <menu name="会员管理" position="888.7-5" icon='bicon-huiyuan'
              function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc">
            <menu name="会员管理" key="member-manager"
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  path="sale/member/pages/vip/manage/VipManage.gspx?closeType=closeAll" isnew="true"
                  position="888.7-5-1" icon="bicon-huiyuanguanli" permission-key="member.vip.view">

                <permission key="member.vip.detail" roles="运营,销售" name="会员详情"/>
                <permission key="member.vip.view" roles="运营,销售" name="查看"/>
                <permission key="member.vip.add" roles="运营,销售" name="新增" depend="member.vip.view"/>
                <permission key="member.vip.delete" roles="运营,销售" name="删除" depend="member.vip.view"/>
                <permission key="member.vip.edit" roles="运营,销售" name="编辑" depend="member.vip.view"/>
                <permission key="member.vip.giveScore" roles="运营，销售" name="赠送积分" depend="member.vip.view"/>
                <permission key="member.vip.bindRightsCard" roles="运营，销售" name="发放权益卡"
                            depend="member.vip.view"/>
                <permission key="member.vip.card.delete" roles="运营，销售" name="解绑权益卡"
                            depend="member.vip.view"/>
                <permission key="member.vip.card" roles="运营，销售" name="发放优惠券" depend="member.vip.view"/>
                <permission key="member.vip.coupon.delete" roles="运营，销售" name="解绑优惠券"
                            depend="member.vip.view"/>
                <!--                    <permission key="member.vip.recharge" roles="运营，销售" name="充值" depend="member.vip.view"/>-->
                <permission key="member.vip.export" roles="运营，销售" name="会员导出" depend="member.vip.view"/>
                <permission key="member.vip.import" roles="运营，销售" name="会员导入" depend="member.vip.add"/>
                <permission key="member.vip.addTags" roles="运营，销售" name="加标签" depend="member.vip.view"/>
                <permission key="member.vip.rechargeRefund" roles="运营，销售" name="储值退款"
                            depend="member.vip.view"/>
                <permission key="member.vip.download" roles="运营，销售" name="下载会员" depend="member.vip.view"/>
                <permission key="member.vip.registerQrCode" roles="运营，销售" name="扫码注册会员"
                            depend="member.vip.view"/>
                <permission key="member.vip.sendsms" roles="运营，销售" name="短信推送" depend="member.vip.view"/>
                <permission key="member.vip.payVipRefund" roles="运营，销售" name="付费会员退款"
                            depend="member.vip.view"/>
                <permission key="member.vip.scoreQuickReason" roles="运营，销售" name="积分调整原因设置" value="true"
                            depend="member.vip.view"/>
                <permission key="member.vip.viewHistoryConsume" roles="运营，销售" name="查看历史购买记录"
                            depend="member.vip.view"/>
                <permission key="member.vip.batchRecharge" roles="运营，销售" name="批量会员充值"
                            depend="member.vip.view"/>
                <permission key="member.rechargeStrategy.editGiveMoney" roles="运营,销售" name="编辑赠送金额"/>
                <permission key="member.vip.modifyLevel" roles="运营，销售" name="修改会员等级"
                            depend="member.vip.view"/>
            </menu>
            <menu name="会员等级" key="sale.member-level-manager"
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  path="sale/member/pages/vip/level/VipLevelManage.gspx"
                  isnew="true"
                  position="888.7-5-2" icon="bicon-huiyuandengjiguanli" permission-key="member.level.view">
                <permission key="member.level.view" roles="运营,销售" name="查看"/>
                <permission key="member.level.add" roles="运营,销售" name="新增" depend="member.level.view"/>
                <permission key="member.level.edit" roles="运营,销售" name="编辑" depend="member.level.view"/>
                <permission key="member.level.open" roles="运营,销售" name="启用" depend="member.level.view"/>
                <permission key="member.level.stop" roles="运营,销售" name="停用" depend="member.level.view"/>
                <permission key="member.level.assess" roles="运营,销售" name="等级评估周期"
                            depend="member.level.view"/>
                <permission key="member.level.rule" roles="运营,销售" name="成长值规则" depend="member.level.view"/>
            </menu>
            <menu name="权益卡" key="rightsmanager" path="sale/member/pages/rights/RightsCardManager.gspx"
                  isnew="true" function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  position="888.7-5-3" icon="bicon-quanyikaguanli" permission-key="member.rightsmanager.view">
                <permission key="member.rightsmanager.view" roles="运营,销售" name="查看"/>
                <permission key="member.rightsmanager.add" roles="运营,销售" name="新增"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.edit" roles="运营,销售" name="修改"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.delete" roles="运营,销售" name="删除"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.open" roles="运营,销售" name="启用"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.stop" roles="运营,销售" name="停用"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.showMember" roles="运营,销售" name="查看成员"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rightsmanager.member" roles="运营,销售" name="发卡给会员"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rights.view" roles="运营,销售" name="权益配置查看"/>
                <permission key="member.rights.add" roles="运营,销售" name="权益配置新增"
                            depend="member.rights.view"/>
                <permission key="member.rights.edit" roles="运营,销售" name="权益配置修改"
                            depend="member.rights.view"/>
                <permission key="member.rights.delete" roles="运营,销售" name="权益配置删除"
                            depend="member.rights.view"/>
                <permission key="member.rights.open" roles="运营,销售" name="权益配置启用"
                            depend="member.rights.view"/>
                <permission key="member.rights.stop" roles="运营,销售" name="权益配置停用"
                            depend="member.rights.view"/>

            </menu>
            <menu name="优惠券" key="cardtemplate" path="sale/member/pages/card/CardTemplate.gspx" isnew="true"
                  position="888.7-5-4" icon="bicon-kaquanmoban" permission-key="member.cardtemplate.view"
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc">
                <permission key="member.cardtemplate.view" roles="运营,销售" name="查看"/>
                <permission key="member.cardtemplate.add" roles="运营,销售" name="新增"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.edit" roles="运营,销售" name="编辑"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.delete" roles="运营,销售" name="删除"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.open" roles="运营,销售" name="启用"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.stop" roles="运营,销售" name="停用"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.showMember" roles="运营,销售" name="查看成员"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.creatcard" roles="运营,销售" name="发放优惠券"
                            depend="member.cardtemplate.view"/>
                <permission key="member.cardtemplate.showRecord" roles="运营,销售" name="查看优惠券发放记录"
                            depend="member.cardtemplate.view" value="true"/>

            </menu>
            <menu name="积分策略" key="pointsConfiguration"
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  path="sale/member/pages/vip/points/PointsConfiguration.gspx" isnew="true"
                  position="888.7-5-5" icon="bicon-huiyuanguanli" permission-key="member.pointsConfiguration.view">
                <permission key="member.pointsConfiguration.view" roles="运营,销售" name="查看"/>
                <permission key="member.pointsConfiguration.edit" roles="运营,销售" name="编辑"
                            depend="member.pointsConfiguration.view"/>
            </menu>
            <menu name="储值策略" key="rechargeStrategy"
                  path="sale/member/pages/recharge/RechargeStrategy.gspx"
                  permission-key="member.rechargeStrategy.view"
                  isnew="true" function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  position="888.7-5-6" icon="bicon-chuzhizengsongguize">
                <permission key="member.rechargeStrategy.view" roles="运营,销售" name="查看"/>
                <permission key="member.rechargeStrategy.add" roles="运营,销售" name="新增"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rechargeStrategy.edit" roles="运营,销售" name="编辑"
                            depend="member.rightsmanager.view"/>
                <permission key="member.rechargeStrategy.open" roles="运营,销售" name="启用/停用"
                            depend="member.rightsmanager.view"/>
            </menu>
            <menu name="微信会员" key="weixin-member"
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                  path="sale/member/pages/wx/WeixinMember.gspx" isnew="true"
                  position="888.7-5-7" icon="bicon-weixinhuiyuan" permission-key="weixin.member.view">
                <permission key="weixin.member.view" roles="运营,销售" name="查看"/>
                <permission key="weixin.auth.view" roles="运营,销售" name="公众号授权查看"
                            depend="weixin.member.view"/>
                <permission key="weixin.auth.authorization" roles="运营,销售" name="公众号授权/解绑"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.addTemplate" roles="运营,销售" name="新增会员卡"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.editTemplate" roles="运营,销售" name="修改"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.deleteTemplate" roles="运营,销售" name="删除"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.sendCard" roles="运营,销售" name="发卡"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.refreshAuditState" roles="运营,销售" name="刷新审核状态"
                            depend="weixin.auth.view"/>
                <permission key="weixin.member.wxSync" roles="运营,销售" name="自动同步设置"
                            depend="weixin.auth.view"/>
            </menu>
        </menu>


        <menu name="会员统计分析" position="888.7-6" icon='bicon-tongji'
              function="StoreFunc">
            <menu name="会员储值记录" key="sale.member-recharge-record"
                  function="StoreFunc"
                  path="sale/member/pages/recharge/RechargeRecord.gspx"
                  isnew="true"
                  position="888.7-6-1" icon="bicon-huiyuanchuzhijilu" permission-key="shopsale.rechargeRecord.view">
                <permission key="shopsale.rechargeRecord.view" roles="运营,销售" name="查看"/>
                <permission key="shopsale.rechargeRecord.export" roles="运营,销售" name="导出"
                            depend="shopsale.rechargeRecord.view"/>
            </menu>
            <menu name="会员储值变动统计" key="StoredValueChangeRecord"
                  function="StoreFunc"
                  path="sale/member/pages/vip/statistics/StoredValueChangeRecord.gspx"
                  isnew="true"
                  position="888.7-6-2" icon="bicon-huiyuanchuzhibiandongtongji"
                  permission-key="member.storedValueChangeRecord.view">
                <permission key="member.storedValueChangeRecord.view" roles="运营,销售" name="查看"/>
                <permission key="member.storedValueChangeRecord.export" roles="运营,销售" name="导出"
                            depend="member.storedValueChangeRecord.view"/>
            </menu>
            <menu name="会员积分变动统计" key="ScoreChangeRecord"
                  function="StoreFunc"
                  path="sale/member/pages/vip/statistics/ScoreChangeRecord.gspx"
                  isnew="true"
                  position="888.7-6-3" icon="bicon-huiyuanjifenbiandongtongji"
                  permission-key="member.scoreChangeRecord.view">
                <permission key="member.scoreChangeRecord.view" roles="运营,销售" name="查看"/>
                <permission key="member.scoreChangeRecord.export" roles="运营,销售" name="导出"
                            depend="member.scoreChangeRecord.view"/>
            </menu>
            <menu name="会员积分分析" key="ScoreAnalysisRecord"
                  function="StoreFunc"
                  path="sale/member/pages/vip/statistics/scoreAnalysis/ScoreAnalysisRecord.gspx"
                  isnew="true"
                  position="888.7-6-4" icon="bicon-huiyuanjifenfenxi"
                  permission-key="member.scoreAnalysisRecord.view">
                <permission key="member.scoreAnalysisRecord.view" roles="运营,销售" name="查看"/>
            </menu>
            <menu name="会员储值分析" key="StoredValueAnalysisRecord"
                  function="StoreFunc"
                  path="sale/member/pages/vip/statistics/storedValueAnalysis/StoredValueAnalysisRecord.gspx"
                  isnew="true"
                  position="888.7-6-5" icon="bicon-huiyuanchuzhijilu"
                  permission-key="member.storedValueAnalysisRecord.view">
                <permission key="member.storedValueAnalysisRecord.view" roles="运营,销售" name="查看"/>
            </menu>
        </menu>

        <!-- **************     会员菜单end     **************-->
    </menus>
</root>
